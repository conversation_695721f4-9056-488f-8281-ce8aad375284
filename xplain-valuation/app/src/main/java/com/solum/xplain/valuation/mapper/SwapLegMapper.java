package com.solum.xplain.valuation.mapper;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING;
import static com.solum.xplain.valuation.mapper.AccrualScheduleMapper.accrualSchedule;
import static com.solum.xplain.valuation.mapper.TradeMapperUtils.parseIborIndex;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.PriceIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.FixedAccrualMethod;
import com.opengamma.strata.product.swap.FixedRateCalculation;
import com.opengamma.strata.product.swap.FutureValueNotional;
import com.opengamma.strata.product.swap.IborRateCalculation;
import com.opengamma.strata.product.swap.InflationRateCalculation;
import com.opengamma.strata.product.swap.NotionalSchedule;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.opengamma.strata.product.swap.OvernightRateCalculation;
import com.opengamma.strata.product.swap.PaymentSchedule;
import com.opengamma.strata.product.swap.PriceIndexCalculationMethod;
import com.opengamma.strata.product.swap.RateCalculation;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.solum.xplain.extensions.index.OffshoreIndices;
//import com.solum.xplain.extensions.index.OvernightTermIndex;
//import com.solum.xplain.extensions.index.OvernightTermRateCalculation;
import com.solum.xplain.valuation.messages.trade.ValuationTradeDetails;
import com.solum.xplain.valuation.messages.trade.ValuationTradeLegDetails;
import java.util.Optional;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

@Component
public class SwapLegMapper {

  public RateCalculationSwapLeg toStrataLeg(
      ValuationTradeLegDetails leg, ValuationTradeDetails details, PayReceive payReceive) {
    var accrualSchedule =
        accrualSchedule(
            details.getStartDate(), details.getEndDate(), details, leg.getAccrualFrequency());

    return RateCalculationSwapLeg.builder()
        .payReceive(payReceive)
        .accrualSchedule(accrualSchedule)
        .paymentSchedule(paymentSchedule(details, leg))
        .notionalSchedule(notionalSchedule(details, leg))
        .calculation(toRateCalculation(leg))
        .build();
  }

  private PaymentSchedule paymentSchedule(
      ValuationTradeDetails trade, ValuationTradeLegDetails leg) {
    var calendar = HolidayCalendarId.of(trade.getCalendar());
    var offsetAdjustment = BusinessDayAdjustment.of(FOLLOWING, calendar);
    var paymentScheduleAdjustment =
        StrataMappingUtils.adjustment(calendar, trade.getBusinessDayConvention());
    return PaymentSchedule.builder()
        .paymentFrequency(Frequency.parse(leg.getPaymentFrequency()))
        .businessDayAdjustment(paymentScheduleAdjustment)
        .paymentDateOffset(
            DaysAdjustment.builder()
                .days(leg.getPaymentOffsetDays())
                .calendar(calendar)
                .adjustment(offsetAdjustment)
                .build())
        .compoundingMethod(
            isNullOrEmpty(leg.getPaymentCompounding())
                ? CompoundingMethod.NONE
                : CompoundingMethod.of(leg.getPaymentCompounding()))
        .build();
  }

  private NotionalSchedule notionalSchedule(
      ValuationTradeDetails trade, ValuationTradeLegDetails leg) {
    return NotionalSchedule.builder()
        .currency(Currency.of(leg.getCurrency()))
        .amount(ValueSchedule.of(leg.getNotional()))
        .initialExchange(BooleanUtils.isTrue(trade.getNotionalScheduleInitialExchange()))
        .finalExchange(BooleanUtils.isTrue(trade.getNotionalScheduleFinalExchange()))
        .build();
  }

  private RateCalculation toRateCalculation(ValuationTradeLegDetails leg) {
    return switch (leg.getType()) {
      case FIXED -> toFixedRateCalculation(leg);
      case IBOR -> toIborRateCalculation(leg);
      case INFLATION -> toInflationRateCalculation(leg);
      case OVERNIGHT, TERM_OVERNIGHT -> toOvernightRateCalculation(leg);
    };
  }

  private RateCalculation toFixedRateCalculation(ValuationTradeLegDetails leg) {
    var futureValueNotional =
        Optional.ofNullable(leg.getAccrualMethod())
            .map(FixedAccrualMethod::of)
            .filter(FixedAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE::equals)
            .map(v -> FutureValueNotional.autoCalculate())
            .orElse(null);
    return FixedRateCalculation.builder()
        .dayCount(DayCount.of(leg.getDayCount()))
        .rate(ValueSchedule.of(leg.getInitialValue()))
        .futureValueNotional(futureValueNotional)
        .build();
  }

  private RateCalculation toIborRateCalculation(ValuationTradeLegDetails leg) {
    var iborIndex = parseIborIndex(leg.getIndex(), leg.getIsOffshore());

    var daysAdjustment =
        Optional.ofNullable(leg.getFixingDateOffsetDays())
            .map(
                days ->
                    DaysAdjustment.builder()
                        .days(days)
                        .calendar(iborIndex.getFixingDateOffset().getCalendar())
                        .adjustment(iborIndex.getFixingDateOffset().getAdjustment())
                        .build()
                        .normalized())
            .orElse(iborIndex.getFixingDateOffset());

    return IborRateCalculation.builder()
        .dayCount(DayCount.of(leg.getDayCount()))
        .index(iborIndex)
        .fixingDateOffset(daysAdjustment)
        .spread(valueSchedule(leg.getInitialValue()))
        .build();
  }

  private ValueSchedule valueSchedule(Double value) {
    if (value == null) {
      return null;
    }
    return ValueSchedule.of(value);
  }

  private RateCalculation toInflationRateCalculation(ValuationTradeLegDetails leg) {
    var priceIndex = PriceIndex.of(leg.getIndex());
    var indexCalcMethod = PriceIndexCalculationMethod.of(leg.getIndexCalculationMethod());

    return InflationRateCalculation.builder()
        .lag(Tenor.parse(leg.getInflationLag()).getPeriod())
        .index(priceIndex)
        .indexCalculationMethod(indexCalcMethod)
        .build();
  }

  private RateCalculation toOvernightRateCalculation(ValuationTradeLegDetails leg) {
    var overnightAccrual =
        leg.getAccrualMethod() == null
            ? OvernightAccrualMethod.COMPOUNDED
            : OvernightAccrualMethod.of(leg.getAccrualMethod());

    var rateCutOffDays = leg.getOvernightRateCutOffDays() == null ? 0 : leg.getOvernightRateCutOffDays();
    return OvernightRateCalculation.builder()
        .dayCount(DayCount.of(leg.getDayCount()))
        .index(parseOvernight(leg.getIndex(), leg.getIsOffshore()))
        .spread(valueSchedule(leg.getInitialValue()))
        .accrualMethod(overnightAccrual)
        .rateCutOffDays(rateCutOffDays)
        .build();
  }

  private OvernightIndex parseOvernight(String index, Boolean offshoreLeg) {
    var overnightIndex = OvernightIndex.of(index);

    if (BooleanUtils.isTrue(offshoreLeg)) {
      return OffshoreIndices.lookupOffshoreOvernight(overnightIndex).orElseThrow();
    }
    return overnightIndex;
  }
}
