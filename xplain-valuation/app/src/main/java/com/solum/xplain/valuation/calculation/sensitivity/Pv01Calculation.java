package com.solum.xplain.valuation.calculation.sensitivity;

import static com.google.common.math.DoubleMath.fuzzyEquals;
import static com.opengamma.strata.product.swap.Swap.of;
import static com.solum.xplain.valuation.calculation.Constants.ONE_BASIS_POINT;
import static com.solum.xplain.valuation.calculation.Constants.ZERO_TOLERANCE;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.CurrencyAmount;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.pricer.swap.DiscountingSwapTradePricer;
import com.opengamma.strata.product.ResolvableTrade;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.FixedRateCalculation;
import com.opengamma.strata.product.swap.IborRateCalculation;
import com.opengamma.strata.product.swap.InflationRateCalculation;
import com.opengamma.strata.product.swap.OvernightRateCalculation;
import com.opengamma.strata.product.swap.RateCalculation;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.opengamma.strata.product.swap.ResolvedSwapTrade;
import com.opengamma.strata.product.swap.SwapLeg;
import com.opengamma.strata.product.swap.SwapTrade;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

/** TODO: SB-409 Refactor Pv01 calculation for Valuations */
public class Pv01Calculation {

  public static final Pv01Calculation DEFAULT = new Pv01Calculation();

  private Pv01Calculation() {}

  public static Optional<SwapTrade> buildShiftedSwapTrade(SwapTrade trade) {
    long fixedLegsCount =
        trade.getProduct().getLegs().stream().filter(a -> a.getType().isFixed()).count();
    long inflationLegsCount =
        trade.getProduct().getLegs().stream()
            .filter(RateCalculationSwapLeg.class::isInstance)
            .map(RateCalculationSwapLeg.class::cast)
            .map(RateCalculationSwapLeg::getCalculation)
            .filter(InflationRateCalculation.class::isInstance)
            .count();
    if (inflationLegsCount == 2) {
      return Optional.empty();
    } else if (fixedLegsCount >= 1) {
      return Optional.of(buildShiftedSwapTradeWithShiftedFixedLeg(trade, fixedLegsCount == 2));
    } else {
      return Optional.of(buildShiftedSwapTradeWithShiftedFloatLeg(trade, inflationLegsCount == 1));
    }
  }

  private static SwapTrade buildShiftedSwapTradeWithShiftedFixedLeg(
      SwapTrade trade, boolean bothLegsFixed) {
    return trade.toBuilder()
        .product(
            of(
                trade.getProduct().getLegs().stream()
                    .filter(RateCalculationSwapLeg.class::isInstance)
                    .map(RateCalculationSwapLeg.class::cast)
                    .map(l -> buildSwapFixedLeg(l, bothLegsFixed))
                    .toList()))
        .build();
  }

  public static RateCalculationSwapLeg buildSwapFixedLeg(
      RateCalculationSwapLeg l, boolean bothLegsFixed) {
    if (bothLegsFixed && l.getPayReceive().isPay() || !bothLegsFixed && l.getType().isFixed()) {
      FixedRateCalculation calculation = (FixedRateCalculation) l.getCalculation();
      double shiftedRate = calculation.getRate().getInitialValue() + ONE_BASIS_POINT;
      return l.toBuilder()
          .calculation(calculation.toBuilder().rate(ValueSchedule.of(shiftedRate)).build())
          .build();
    } else {
      return l.toBuilder().build();
    }
  }

  private static SwapTrade buildShiftedSwapTradeWithShiftedFloatLeg(
      SwapTrade trade, boolean inflationLegExists) {
    PayReceive legToShift = legToShift(trade.getProduct().getLegs().stream(), inflationLegExists);

    var swap =
        of(
            trade.getProduct().getLegs().stream()
                .filter(RateCalculationSwapLeg.class::isInstance)
                .map(RateCalculationSwapLeg.class::cast)
                .map(l -> buildSwapFloatLeg(l, legToShift))
                .toList());
    return trade.toBuilder().product(swap).build();
  }

  private static RateCalculationSwapLeg buildSwapFloatLeg(
      RateCalculationSwapLeg l, PayReceive legToShift) {
    if (l.getPayReceive().equals(legToShift)) {
      if (l.getCalculation() instanceof IborRateCalculation) {
        return buildShiftedSwapFloatIborLeg(l);
      } else if (l.getCalculation() instanceof OvernightRateCalculation) {
        return buildShiftedSwapFloatOvernightLeg(l);
      } else {
        throw new IllegalArgumentException(
            "Unsupported rate calculation type: " + l.getCalculation().getClass());
      }
    } else {
      return l.toBuilder().build();
    }
  }

  private static RateCalculationSwapLeg buildShiftedSwapFloatIborLeg(RateCalculationSwapLeg l) {
    IborRateCalculation calculation = (IborRateCalculation) l.getCalculation();
    return l.toBuilder()
        .calculation(
            calculation.toBuilder()
                .spread(ValueSchedule.of(getLegSpread(calculation) + ONE_BASIS_POINT))
                .build())
        .build();
  }

  private static RateCalculationSwapLeg buildShiftedSwapFloatOvernightLeg(
      RateCalculationSwapLeg l) {
    OvernightRateCalculation calculation = (OvernightRateCalculation) l.getCalculation();
    return l.toBuilder()
        .calculation(
            calculation.toBuilder()
                .spread(ValueSchedule.of(getLegSpread(calculation) + ONE_BASIS_POINT))
                .build())
        .build();
  }

  private static PayReceive legToShift(Stream<SwapLeg> legs, boolean inflationLegExists) {
    return inflationLegExists
        ? legs.filter(RateCalculationSwapLeg.class::isInstance)
            .map(RateCalculationSwapLeg.class::cast)
            .filter(
                l ->
                    l.getCalculation() instanceof IborRateCalculation
                        || l.getCalculation() instanceof OvernightRateCalculation)
            .map(RateCalculationSwapLeg::getPayReceive)
            .findAny()
            .orElse(null)
        : legs.filter(RateCalculationSwapLeg.class::isInstance)
            .map(RateCalculationSwapLeg.class::cast)
            .filter(
                l ->
                    l.getPayReceive().isPay()
                        && !fuzzyEquals(getLegSpread(l.getCalculation()), 0d, ZERO_TOLERANCE))
            .map(RateCalculationSwapLeg::getPayReceive)
            .findAny()
            .orElse(PayReceive.RECEIVE);
  }

  private static double getLegSpread(RateCalculation calculation) {
    if (calculation instanceof IborRateCalculation) {
      return ((IborRateCalculation) calculation)
          .getSpread()
          .map(ValueSchedule::getInitialValue)
          .orElse(0d);
    } else if (calculation instanceof OvernightRateCalculation) {
      return ((OvernightRateCalculation) calculation)
          .getSpread()
          .map(ValueSchedule::getInitialValue)
          .orElse(0d);
    } else {
      throw new IllegalArgumentException(
          "Unsupported rate calculation type: " + calculation.getClass());
    }
  }

  public Optional<CurrencyAmount> calculatePv01(
      ResolvableTrade<?> trade,
      CurrencyAmount pv,
      double tZeroNet,
      RatesProvider rates,
      ReferenceData refData,
      DiscountingSwapTradePricer swapTradePricer,
      Function<ResolvedSwapTrade, Double> tZeroNetFn) {
    if (pv == null) {
      return Optional.empty();
    }
    if (trade instanceof SwapTrade) {
      return buildShiftedSwapTrade((SwapTrade) trade)
          .map(
              shiftedTrade -> {
                var cfAdjPv = pv.minus(tZeroNet);

                var resolvedShiftedTrade = shiftedTrade.resolve(refData);
                // Using the same pricer for the shifted trade as the original trade
                var shiftedPv =
                    swapTradePricer.presentValue(resolvedShiftedTrade, pv.getCurrency(), rates);

                var shiftedTZeroNet = tZeroNetFn.apply(resolvedShiftedTrade);
                var cfAdjShiftedPv = shiftedPv.minus(shiftedTZeroNet);

                return cfAdjShiftedPv.minus(cfAdjPv);
              });
    } else {
      throw new IllegalArgumentException(
          "PV01 calculation is only available for IRS and XCCY trades at the moment!");
    }
  }
}
