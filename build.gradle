buildscript {
  apply from: 'dependencies.gradle'
  apply from: "gradle/git-hooks.gradle"
}

plugins {
  id 'com.diffplug.spotless' version "${spotlessVersion}"
  id 'org.owasp.dependencycheck' version "${dependencyCheckVersion}" apply false
  id 'io.spring.dependency-management' version "${dependencyManagementVersion}"
}

repositories {
  mavenCentral()
}

def isCiServer = System.getenv().containsKey("CI")
def runITs = !project.hasProperty("skipITs")
def runUnitTests = !project.hasProperty("skipUnitTests")

subprojects {
  group = 'com.solum.xplain'
  version = rootProject.version

  apply plugin: 'java'
  apply plugin: 'jvm-test-suite'
  apply plugin: 'io.spring.dependency-management'
  apply plugin: 'groovy'
  apply plugin: 'jacoco'
  apply plugin: 'idea'
  apply plugin: 'org.owasp.dependencycheck'
  apply plugin: 'com.diffplug.spotless'

  compileJava {
    sourceCompatibility = 21
    targetCompatibility = 21
    options.compilerArgs = [
        '-Amapstruct.unmappedTargetPolicy=ERROR',
        '-Amapstruct.defaultComponentModel=spring',
        '-parameters'
    ]
  }

  //Force higher SnakeYaml
  ext['hazelcast.version'] = "${hazelcastVersion}"
  configurations {
    all*.exclude module: 'spring-boot-starter-logging'
    runtimeClasspath.exclude group: 'com.google.code.findbugs'
  }

  dependencyManagement {
    imports {
      mavenBom("org.springframework.boot:spring-boot-dependencies:${springBootVersion}")
      mavenBom "software.amazon.awssdk:bom:${awsSdkVersion}"
      mavenBom "io.mongock:mongock-bom:${mongockVersion}"
    }
    dependencies {
      dependency "org.jetbrains:annotations:${jetbrainsAnnotationsVersion}"
    }
  }

  dependencies {
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    compileOnly "org.jetbrains:annotations"
    compileOnly "org.jspecify:jspecify:1.0.0"

    implementation "org.apache.commons:commons-lang3"
    implementation "org.apache.commons:commons-collections4:${commonsCollections4Version}"

    implementation("com.google.guava:guava:${guavaVersion}")
    implementation "io.atlassian.fugue:fugue:${fugueVersion}"
    implementation "io.atlassian.fugue:fugue-extensions:${fugueVersion}"
    implementation "org.mapstruct:mapstruct:${mapstructVersion}"
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${springDocVersion}"
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'

    constraints {
      implementation('org.xerial.snappy:snappy-java') {
        version {
          require '1.1.10.4'
        }
        because 'CVE-2023-43642'
      }
    }

    constraints {
      implementation('com.nimbusds:nimbus-jose-jwt') {
        version {
          require '9.37.3'
        }
        because 'CVE-2023-52428'
      }
    }

    constraints {
      implementation('org.apache.commons:commons-compress') {
        version {
          require '1.26.0'
        }
        because 'CVE-2024-25710 and CVE-2024-26308'
      }
    }

    implementation 'org.springframework.boot:spring-boot-starter-log4j2'
    implementation "io.sentry:sentry-log4j2:${sentrySdkVersion}"
    implementation "org.apache.logging.log4j:log4j-layout-template-json"

    testCompileOnly "org.projectlombok:lombok:${lombokVersion}"
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation "org.apache.groovy:groovy-json:${groovyJsonVersion}"
    testImplementation "org.spockframework:spock-core:${spockVersion}"
    testImplementation "org.spockframework:spock-spring:${spockVersion}"
    testImplementation "org.testcontainers:mongodb"

    testRuntimeOnly "org.junit.platform:junit-platform-launcher"

    implementation "jakarta.inject:jakarta.inject-api:2.0.1"

    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok-mapstruct-binding:${lombokMapstructBindingVersion}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
  }

  repositories {
    mavenCentral()
    // maven {url "https://repo.spring.io/snapshot" }
    // maven {url "https://repo.spring.io/milestone" }
    maven {
      url = uri("https://maven.pkg.github.com/SolumXplain/solum-xplain-opengamma-strata")
      credentials {
        username = "x-access-token"
        password = project.findProperty("GITHUB_TOKEN") as String ?: System.getenv("GITHUB_TOKEN")
      }
    }
  }

  sourceSets {
    testWip {
      groovy {
        srcDirs = ['src/testWip/groovy']
        compileClasspath = sourceSets.test.compileClasspath + sourceSets.main.compileClasspath + sourceSets.test.output
        runtimeClasspath = sourceSets.test.runtimeClasspath + sourceSets.main.runtimeClasspath + sourceSets.test.output
      }
    }
  }

  testing {
    suites {
      test {
        useJUnitJupiter()

        targets {
          configureEach {
            testTask.configure {
              enabled = runUnitTests
              systemProperty 'user.timezone', 'UTC'
            }
          }
        }
      }

      integrationTest(JvmTestSuite) {
        dependencies {
          implementation project()
          implementation "org.projectlombok:lombok:${lombokVersion}"
          implementation "org.testcontainers:mongodb"
          implementation "org.apache.groovy:groovy-json:${groovyJsonVersion}"
          implementation "org.spockframework:spock-core:${spockVersion}"
          implementation "org.spockframework:spock-spring:${spockVersion}"
        }

        targets {
          configureEach {
            testTask.configure {
              enabled = runITs
              systemProperty 'user.timezone', 'UTC'
            }
          }
        }
      }
    }
  }
  tasks.named('check') {
      dependsOn(testing.suites.integrationTest)
  }


  jacocoTestReport {
    mustRunAfter(test)
    mustRunAfter(integrationTest)
    def list = []
    def dir = new File("${project.buildDir}/jacoco")
    if(dir.exists()) {
      dir.eachFileRecurse(groovy.io.FileType.FILES) { file ->
        /* gather all the "exec" files available */
        if (file.getName().endsWith(".exec")) {
          list << file.getAbsolutePath()
        }
      }
      /* provide all the "exec" files to jacoco */
      executionData.from = files(list)
    }
    reports {
      xml.required = true
      html.required = true
    }
  }

  spotless {
    if (runITs) {
      java {
        toggleOffOn()
        googleJavaFormat('1.28.0')
      }

      if (!isCiServer) {
        groovy {
          toggleOffOn()
          excludeJava()
          importOrder('\\#', '')
          removeSemicolons()
          greclipse('4.30').configFile rootProject.file("greclipse.properties")
        }

        groovyGradle {
          target '*.gradle' // default target of groovyGradle
          leadingTabsToSpaces()
          greclipse('4.30').configFile rootProject.file("greclipse.properties")
        }
      }
    }
  }

  dependencyCheck {
    data {
      directory="${project.rootDir}/dependency-check/data"
    }
    nvd {
      apiKey = project.findProperty("NIST_NVD_API_KEY") as String ?: System.getenv("NIST_NVD_API_KEY")
    }

    format = "HTML"
    failBuildOnCVSS = 7
    suppressionFile = "owasp-suppressions.xml"
  }

}
