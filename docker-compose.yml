services:
  solum_mongo:
    image: mongo:7
    ulimits:
      memlock:
        soft: -1
        hard: -1
    deploy:
      resources:
        limits:
          memory: 1g
    volumes:
      - solum_mongo_data:/data/db
    ports:
      - 27017:27017
  xplain_ui:
    image: 575153581909.dkr.ecr.eu-west-2.amazonaws.com/solum-xplain-ui:main
    network_mode: "host"
    volumes:
      - ./nginx-for-api-dev.conf:/etc/nginx/nginx.conf:ro
      - ./ui-env.js:/usr/share/nginx/html/env.js:ro
  xplain_kafka:
    image: confluentinc/cp-kafka:7.5.5
    ports:
      - 29092:29092
    environment:
      CLUSTER_ID: MkU3OEVBNT8a236c3c9c19 # could be ${KAFKA_CLUSTER_ID}
      KAFKA_CONTROLLER_QUORUM_VOTERS: '1@xplain_kafka:29093'
      KAFKA_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_PROCESS_ROLES: broker,controller
      <PERSON><PERSON><PERSON>_BROKER_ID: 1
      KAFKA_ADVERTISED_LISTENERS: SASL_PLAINTEXT://xplain_kafka:9092,SASL_PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENERS: 'SASL_PLAINTEXT://xplain_kafka:9092,CONTROLLER://xplain_kafka:29093,SASL_PLAINTEXT_HOST://0.0.0.0:29092'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: CONTROLLER:PLAINTEXT,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_PLAINTEXT_HOST:SASL_PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: SASL_PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_OPTS: "-Djava.security.auth.login.config=/etc/kafka/kafka_server_jaas.conf"
      KAFKA_SASL_ENABLED_MECHANISMS: PLAIN
      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: PLAIN
    volumes:
      - ./kafka_server_jaas.conf:/etc/kafka/kafka_server_jaas.conf
  xplain_kafka_ui:
    container_name: xplain_kafka-ui
    image: provectuslabs/kafka-ui:latest
    ports:
      - 8070:8080
    depends_on:
      - xplain_kafka
    environment:
      KAFKA_CLUSTERS_0_NAME: xplain
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: xplain_kafka:9092
      KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL: SASL_PLAINTEXT
      KAFKA_CLUSTERS_0_PROPERTIES_SASL_MECHANISM: PLAIN
      KAFKA_CLUSTERS_0_PROPERTIES_SASL_JAAS_CONFIG: 'org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin-secret";'
    profiles:
      - kafka-ui
  xplain_hazelcast:
    image: hazelcast/hazelcast:5.3.6
    ports:
      - 5701:5701
  xplain_hazelcast_management:
    image: hazelcast/management-center:latest
    container_name: xplain_hazelcast_management
    depends_on:
      - xplain_hazelcast
    ports:
      - 8701:8080
    environment:
      MC_CLUSTER_MEMBERS: xplain_hazelcast
    profiles:
      - management-center
  xplain_redis:
    image: redis:7.4.2
    ports:
      - 6379:6379
  xplain_redis_insight:
    image: redis/redisinsight:latest
    container_name: xplain_redis-insight
    depends_on:
      - xplain_redis
    ports:
      - 5540:5540
    profiles:
      - redis-insight
  xplain_api_valuation:
    image: 575153581909.dkr.ecr.eu-west-2.amazonaws.com/solum-xplain-valuation:main
    container_name: xplain_api_valuation
    depends_on:
      - xplain_kafka
      - xplain_hazelcast
    environment:
      SPRING_KAFKA_BOOTSTRAP_SERVERS: xplain_kafka:9092
      HZCLIENT_NETWORK_CLUSTER-MEMBERS_ADDRESS: xplain_hazelcast
    profiles:
      - valuation
  xplain_setup:
    image: 575153581909.dkr.ecr.eu-west-2.amazonaws.com/solum-xplain-setup:main
    container_name: xplain_setup
    ports:
      - 8083:8080
  mock_oauth:
    image: ghcr.io/navikt/mock-oauth2-server:0.5.1
    hostname: oauth2
    environment:
      PORT: 8081
      JSON_CONFIG: '{
        "interactiveLogin": false,
        "httpServer": "NettyWrapper",
        "tokenCallbacks": [
          {
            "issuerId": "default",
            "tokenExpiry": 43200,
            "requestMappings": [
              {
                "requestParam": "audience",
                "match": "api",
                "claims": {
                  "azp": "DEBUGGER",
                  "sub": "DevUser",
                  "gty" : "client-credentials",
                  "xplain/roles-teams" : ["ROLE_ADMIN", "TEAM_DEFAULT"],
                  "xplain/username" : "DevUserName"
                }
              }
            ]
          }
        ]
      }'
    ports:
      - 8081:8081
volumes:
  solum_mongo_data:
    driver: local
