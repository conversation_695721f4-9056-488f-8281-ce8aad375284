<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ValuationApplication (hazelcast)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <module name="solum-xplain-api.xplain-valuation.app.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.solum.xplain.valuation.ValuationApplication" />
    <option name="VM_PARAMETERS" value="-Dspring.profiles.active=dev,local" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>