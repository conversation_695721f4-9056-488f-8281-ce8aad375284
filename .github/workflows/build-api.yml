name: Xplain API Build

on:
  workflow_dispatch:
  push:
    branches:
      - 'SXSD-**'
      - 'release-**'
      - 'main'
    tags:
      - 'v*'
    paths:
      # Main Code
      - 'xplain-api/**'
      - 'shared/**'
      # Configs
      - 'build.gradle'
      - 'dependencies.gradle'
      - 'gradle.properties'
      - 'gradle/gradle-wrapper.properties'
      # Pipeline changes
      - '.github/workflows/build-api.yml'
      - '.github/actions/build-java/action.yml'
      - '.github/actions/build-docker/action.yml'
permissions:
  contents: read
  checks: write
  id-token: write
env:
  AWS_REGION: 'eu-west-2'
  EKS_CLUSTER_NAME: 'xplain'
  PLATFORM_DIR: "platform"
  PLATFORM_BRANCH: "main"
  PROJECT: "solum-xplain-api"
  MODULE_NAME: "xplain-api:app"
  SONAR_MODULE: "xplain-api"
jobs:
  configure:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      branch: ${{ steps.version.outputs.branch }}
      env: ${{ steps.deployment.outputs.environment }}
    steps:
      - uses: actions/checkout@v5
      - name: Checkout platform repository
        uses: actions/checkout@v5
        with:
          path: ${{ env.PLATFORM_DIR }}
          ref: ${{ env.PLATFORM_BRANCH }}
          repository: 'SolumXplain/solum-xplain-platform'
          token: '${{ secrets.PAT_GITHUB_TOKEN }}'
      - uses: ./platform/.github/actions/common/resolve-version
        id: version
        with:
          version-file-path: "gradle.properties"
      - uses: ./platform/.github/actions/common/resolve-env
        id: deployment
        name: "Resolve deployment env"

  build:
    runs-on: build-runner-arm64-static
    needs: [ configure ]
    steps:
      - uses: actions/checkout@v5
      - name: Checkout platform repository
        uses: actions/checkout@v5
        with:
          path: ${{ env.PLATFORM_DIR }}
          ref: ${{ env.PLATFORM_BRANCH }}
          repository: 'SolumXplain/solum-xplain-platform'
          token: '${{ secrets.PAT_GITHUB_TOKEN }}'
      - uses: ./.github/actions/build-java
        with:
          github-token: ${{ secrets.PAT_GITHUB_TOKEN }}
          sonar-token: ${{ secrets.SONAR_TOKEN }}
          module-name: ${{ env.MODULE_NAME }}
          sonar-module: ${{ env.SONAR_MODULE }}
          dockerhub-username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub-token: ${{ secrets.DOCKERHUB_TOKEN }}
      - uses: ./.github/actions/build-docker
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          github-token: ${{ secrets.PAT_GITHUB_TOKEN }}
          branch: ${{ needs.configure.outputs.branch }}
          image-tag: ${{ needs.configure.outputs.version }}
          module-name: ${{ env.MODULE_NAME }}
      - uses: ./platform/.github/actions/release/upload-s3
        if: startsWith(github.ref, 'refs/tags/v')
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          file-to-upload: xplain-api/app/build/libs/app-${{ needs.configure.outputs.version }}.jar
          upload-file-name: solum-xplain-api-app.jar
          version: ${{ needs.configure.outputs.version }}

  deploy:
    runs-on: deploy-runner-arm64-static
    needs: [ configure, build ]
    if: ${{ needs.configure.outputs.env }}
    steps:
      - uses: actions/checkout@v5
      - name: Checkout platform repository
        uses: actions/checkout@v5
        with:
          path: ${{ env.PLATFORM_DIR }}
          ref: ${{ env.PLATFORM_BRANCH }}
          repository: 'SolumXplain/solum-xplain-platform'
          token: '${{ secrets.PAT_GITHUB_TOKEN }}'
      - uses: ./platform/.github/actions/common/deploy
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          cluster-name: ${{ env.EKS_CLUSTER_NAME }}
          environment: ${{ needs.configure.outputs.env }}
          project: ${{ env.PROJECT }}
          image-tag: ${{ needs.configure.outputs.version }}
