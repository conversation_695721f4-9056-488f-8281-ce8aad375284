name: <PERSON><PERSON> Dependabot CI

on:
  push:
    branches:
      - 'dependabot/**'
permissions:
  contents: read
  checks: write
  id-token: write
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5
      - name: Checkout platform repository
        uses: actions/checkout@v5
        with:
          path: 'platform'
          ref: 'main'
          repository: 'SolumXplain/solum-xplain-platform'
          token: '${{ secrets.PAT_GITHUB_TOKEN }}'
      - uses: ./.github/actions/build-java
        with:
          github-token: ${{ secrets.PAT_GITHUB_TOKEN }}
          dockerhub-username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub-token: ${{ secrets.DOCKERHUB_TOKEN }}
