package com.solum.xplain.xm.dashboardsteps.mdoverlayrun


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID

import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository
import spock.lang.Specification

class MdOverlayRunCleanupTest extends Specification {
  ExceptionManagementCalculationRepository repository = Mock()
  DashboardEntryRepository entryRepository = Mock()
  MdOverlayRunCleanup stepCleanup = new MdOverlayRunCleanup(repository, entryRepository)

  def "on execution should invoke repository"() {
    when:
    stepCleanup.execute(MD_DASHBOARD_ID)

    then:
    1 * repository.deleteOverlay(MD_DASHBOARD_ID)
    1 * entryRepository.deleteMdEntries(MD_DASHBOARD_ID, DashboardStep.MD_OVERLAY_RUN)
  }
}
