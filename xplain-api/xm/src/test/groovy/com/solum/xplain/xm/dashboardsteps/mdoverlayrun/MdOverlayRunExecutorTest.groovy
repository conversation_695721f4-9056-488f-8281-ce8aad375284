package com.solum.xplain.xm.dashboardsteps.mdoverlayrun


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE
import static com.solum.xplain.xm.dashboards.enums.DashboardStep.MD_OVERLAY_RUN

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationOverlayService
import io.atlassian.fugue.Either
import spock.lang.Specification

class MdOverlayRunExecutorTest extends Specification {

  private static final BitemporalDate STEP_STATE_DATE = BitemporalDate.newOf(STATE_DATE)

  private static final String RESULT_ID = "exceptionManagementResultId"

  DashboardStepProcessor processor = mockDashboardStepProcessor()
  ExceptionManagementCalculationOverlayService exceptionManagementService = Mock()

  MdOverlayRunExecutor executor = new MdOverlayRunExecutor(processor, exceptionManagementService)

  def setup() {
    processor.getStateDate() >> STEP_STATE_DATE
  }

  def "should run MD exception management overlay"() {
    1 * exceptionManagementService.performOverlayDashboard(
      MARKET_DATA_DASHBOARD,
      STEP_STATE_DATE
      ) >> Either.right(EntityId.entityId(RESULT_ID))

    when:
    def result = executor.execute(MARKET_DATA_DASHBOARD)

    then:
    1 * processor.performMdStep(
      STEP_STATE_DATE,
      { s -> s.dashboardId == MD_DASHBOARD_ID && s.step == MD_OVERLAY_RUN },
      _
      )

    result.isRight()
    result.getOrNull().id == RESULT_ID
  }

  def mockDashboardStepProcessor() {
    DashboardEntryRepository entryRepository = Mock()
    Spy(DashboardStepProcessor, constructorArgs: [entryRepository, Mock(AuditEntryService)]) {
      entryRepository.createEntry(_ as DashboardEntryMd) >> { DashboardEntryMd e -> Either.right(e) }
      entryRepository.updateEntry(_ as DashboardEntryMd) >> { DashboardEntryMd e -> Either.right(e) }
    } as DashboardStepProcessor
  }
}
