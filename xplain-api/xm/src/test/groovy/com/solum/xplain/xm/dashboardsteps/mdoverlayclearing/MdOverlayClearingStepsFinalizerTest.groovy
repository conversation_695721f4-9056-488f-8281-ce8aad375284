package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.CURVE_CONFIGURATION
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.CURVE_CONFIGURATION_ANOTHER
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.UNIQUE_ANOTHER_CURVE_CONFIG_OVERLAY_TASK
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.UNIQUE_COMPANY_ENITY_OVERLAY_TASK
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.UNIQUE_COMPANY_OVERLAY_TASK
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.UNIQUE_CURVE_CONFIG_OVERLAY_TASK
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMarketDataUpload
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdOverlayClearing
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdOverlayRun
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdPreliminaryClearing
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdPreliminaryRun
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.UPDATED

import com.solum.xplain.xm.dashboards.entity.DashboardBuilder
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository
import io.atlassian.fugue.Either
import spock.lang.Specification

class MdOverlayClearingStepsFinalizerTest extends Specification {

  DashboardStepProcessor processor = Mock()
  TaskExecutionRepository taskExecutionRepository = Mock()

  MdOverlayClearingStepsFinalizer finalizer = new MdOverlayClearingStepsFinalizer(processor, taskExecutionRepository)


  def "should update dashboard overlay clearing step for configuration "() {
    def event = MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .curveConfigurationId(CURVE_CONFIGURATION.entityId)
      .build()

    1 * processor.getMdSteps(MARKET_DATA_DASHBOARD) >> entries()

    1 * taskExecutionRepository.allTasksCompleted(UNIQUE_CURVE_CONFIG_OVERLAY_TASK) >> true
    0 * taskExecutionRepository.allTasksCompleted(UNIQUE_ANOTHER_CURVE_CONFIG_OVERLAY_TASK)

    1 * processor.updateMdSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMd>
      assert steps.size() == 1
      assert steps[0].step == DashboardStep.MD_OVERLAY_CLEARING
      assert steps[0].curveConfiguration == CURVE_CONFIGURATION
      assert steps[0].status == StepStatus.COMPLETED
      return Either.right(steps)
    }

    when:
    def result = finalizer.execute(MARKET_DATA_DASHBOARD, event)

    then:
    result.isRight()
  }

  def "should not update dashboard overlay clearing step for configuration when not all tasks completed"() {
    def event = MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .curveConfigurationId(CURVE_CONFIGURATION.entityId)
      .build()
    1 * processor.getMdSteps(MARKET_DATA_DASHBOARD) >> entries()

    1 * taskExecutionRepository.allTasksCompleted(UNIQUE_CURVE_CONFIG_OVERLAY_TASK) >> false
    0 * taskExecutionRepository.allTasksCompleted(UNIQUE_ANOTHER_CURVE_CONFIG_OVERLAY_TASK)

    1 * processor.updateMdSteps([]) >> Either.right([])

    when:
    def result = finalizer.execute(MARKET_DATA_DASHBOARD, event)

    then:
    result.isRight()
  }

  def "should update dashboard overlay clearing step for company"() {
    def event = MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .companyId(DashboardBuilder.COMPANY.entityId)
      .build()

    1 * processor.getMdSteps(MARKET_DATA_DASHBOARD) >> entries()

    1 * taskExecutionRepository.allTasksCompleted(UNIQUE_COMPANY_OVERLAY_TASK) >> true
    0 * taskExecutionRepository.allTasksCompleted(UNIQUE_COMPANY_ENITY_OVERLAY_TASK)

    1 * processor.updateMdSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMd>
      assert steps.size() == 1
      assert steps[0].step == DashboardStep.MD_OVERLAY_CLEARING
      assert steps[0].company == DashboardBuilder.COMPANY
      assert steps[0].status == StepStatus.COMPLETED
      return Either.right(steps)
    }

    when:
    def result = finalizer.execute(MARKET_DATA_DASHBOARD, event)

    then:
    result.isRight()
  }

  def "should update dashboard overlay clearing step for company and entity"() {
    def event = MdOverlayClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .companyId(DashboardBuilder.COMPANY.entityId)
      .legalEntityId(DashboardBuilder.ENTITY.entityId)
      .build()

    1 * processor.getMdSteps(MARKET_DATA_DASHBOARD) >> entries()

    1 * taskExecutionRepository.allTasksCompleted(UNIQUE_COMPANY_ENITY_OVERLAY_TASK) >> true
    0 * taskExecutionRepository.allTasksCompleted(UNIQUE_COMPANY_OVERLAY_TASK)

    1 * processor.updateMdSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMd>
      assert steps.size() == 1
      assert steps[0].step == DashboardStep.MD_OVERLAY_CLEARING
      assert steps[0].company == DashboardBuilder.COMPANY
      assert steps[0].legalEntity == DashboardBuilder.ENTITY
      assert steps[0].status == StepStatus.COMPLETED
      return Either.right(steps)
    }

    when:
    def result = finalizer.execute(MARKET_DATA_DASHBOARD, event)

    then:
    result.isRight()
  }

  static List<DashboardEntryMd> entries() {
    return [
      dashboardStepMarketDataUpload(),
      dashboardStepMdPreliminaryRun(),
      dashboardStepMdPreliminaryClearing(),
      dashboardStepMdOverlayRun(),
      dashboardStepMdOverlayClearing().tap {
        status = StepStatus.IN_PROGRESS
      },
      dashboardStepMdOverlayClearing().tap {
        curveConfiguration = CURVE_CONFIGURATION_ANOTHER
        status = StepStatus.IN_PROGRESS
      },
      dashboardStepMdOverlayClearing().tap {
        curveConfiguration = null
        status = StepStatus.IN_PROGRESS
        company = DashboardBuilder.COMPANY
      },
      dashboardStepMdOverlayClearing().tap {
        curveConfiguration = null
        status = StepStatus.IN_PROGRESS
        company = DashboardBuilder.COMPANY
        legalEntity = DashboardBuilder.ENTITY
      },
    ]
  }
}
