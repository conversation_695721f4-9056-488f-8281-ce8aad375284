package com.solum.xplain.xm.excmngmt.processipv

import static com.solum.xplain.core.users.UserBuilder.user
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslator
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory
import com.solum.xplain.shared.utils.filter.FilterOperation
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.workflow.value.WorkflowStatus
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidenceRepository
import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm
import com.solum.xplain.xm.excmngmt.processipv.view.chart.IpvTaskChartData
import com.solum.xplain.xm.tasks.repository.IpvTaskDefinitionRepository
import com.solum.xplain.xm.workflow.XmWorkflowService
import com.solum.xplain.xm.workflow.repository.XmStepInstanceQueryRepository
import com.solum.xplain.xm.workflow.view.CallActivityProgressView
import java.util.stream.Stream
import org.springframework.data.domain.Sort
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.Authentication
import spock.lang.Specification

class IpvExceptionManagementControllerServiceTest extends Specification {
  def viewQueryTranslator = Mock(ViewQueryTranslator)
  def viewQueryTranslatorFactory = Mock(ViewQueryTranslatorFactory) {
    getTranslator(_ as Class<?>) >> viewQueryTranslator
  }
  def repository = Mock(IpvExceptionManagementCalculationRepository)
  def chartRepository = Mock(IpvExceptionManagementChartRepository)
  def xmStepInstanceQueryRepository = Mock(XmStepInstanceQueryRepository)
  def userRepository = Mock(AuthenticationContext)
  def xmWorkflowService = Mock(XmWorkflowService)
  def controllerService = new IpvExceptionManagementControllerService(
  Mock(ExceptionManagementEvidenceRepository),
  repository,
  chartRepository,
  Mock(IpvTaskDefinitionRepository),
  userRepository,
  viewQueryTranslatorFactory,
  xmStepInstanceQueryRepository,
  xmWorkflowService)

  def "should return overlay items using translated query"() {
    given:
    def filter = TableFilter.emptyTableFilter()
    def unconstrained = ScrollRequest.unconstrained()
    def mappedFilter = new TableFilter([new SimpleFilterClause("mapped", FilterOperation.EQUAL, "true")])
    def mappedScroll = ScrollRequest.of(1, 10, Sort.by("mapped"))
    def form = new ResultDisplayFilterForm(true, false, false)
    repository.overlayItems(["1", "2"], form, mappedFilter, mappedScroll) >> ScrollableEntry.empty()

    when:
    def result = controllerService.overlayItems(["1", "2"], form, filter, unconstrained)

    then:
    result == ScrollableEntry.empty()
    1 * viewQueryTranslator.translate(filter) >> mappedFilter
    1 * viewQueryTranslator.translate(unconstrained) >> mappedScroll
  }

  def "should query repository for charts"() {
    when:
    def result = controllerService.taskCharts([])

    then:
    1 * chartRepository.chartData([]) >> new IpvTaskChartData([])

    and:
    result == new IpvTaskChartData([])
  }

  def "should query repository for progress"() {
    when:
    def result = controllerService.overlayProgress("key")

    then:
    1 * xmStepInstanceQueryRepository.getVdEntryProgress("key") >> new CallActivityProgressView(totalCount: 30)

    and:
    result.totalCount == 30
  }

  def "should undo resolution"() {
    setup:
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)

    1 * userRepository.userEither(_ as Authentication) >> right(user)

    1 * xmWorkflowService.filterVdBusinessKeysByStepStatusAndUser(["resultId"], [WorkflowStatus.FINALIZING, WorkflowStatus.HELD], user) >> Stream.of("key1")

    1 * xmWorkflowService.clearVdResolution(["key1"])

    when:
    def result = controllerService.undoResolution(auth, ["taskId"], null, TableFilter.emptyTableFilter(), ["resultId"])

    then:
    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key1"]
    }
  }

  def "should undo approval"() {
    setup:
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)

    1 * userRepository.userEither(_ as Authentication) >> right(user)

    1 * xmWorkflowService.filterVdBusinessKeysByStepStatusAndUser(["resultId"], [WorkflowStatus.FINALIZING, WorkflowStatus.HELD], user) >> Stream.of("key1")

    1 * xmWorkflowService.clearVdApproval(["key1"])

    when:
    def result = controllerService.undoApproval(auth, ["taskId"], null, TableFilter.emptyTableFilter(), ["resultId"])

    then:
    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key1"]
    }

    when:
    def resultWithoutTaskIds = controllerService.undoApproval(auth, [], null, TableFilter.emptyTableFilter(), ["resultId"])

    then:
    resultWithoutTaskIds.isLeft()
    def error = resultWithoutTaskIds.left().getOrNull() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "No entries found to which this action could be applied."
  }
}
