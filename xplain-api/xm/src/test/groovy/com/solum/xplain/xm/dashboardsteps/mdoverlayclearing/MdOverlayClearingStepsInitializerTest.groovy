package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.CURVE_CONFIGURATION
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult
import com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus
import com.solum.xplain.xm.tasks.entity.TaskExecution
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus
import com.solum.xplain.xm.tasks.service.MdTaskExecutionService
import io.atlassian.fugue.Either
import spock.lang.Specification

class MdOverlayClearingStepsInitializerTest extends Specification {

  private static final BitemporalDate STEP_STATE_DATE = BitemporalDate.newOf(STATE_DATE)

  private static final String RESULT_ID = "exceptionManagementResultId"

  DashboardStepProcessor processor = Mock()
  MdTaskExecutionService taskExecutionService = Mock()
  ExceptionManagementCalculationRepository exceptionManagementRepository = Mock()

  MdOverlayClearingStepsInitializer initializer = new MdOverlayClearingStepsInitializer(processor,
  taskExecutionService,
  exceptionManagementRepository)

  def setup() {
    processor.getStateDate() >> STEP_STATE_DATE
  }

  def "should create steps"() {
    setup:
    1 * exceptionManagementRepository.entityByDashboard(MD_DASHBOARD_ID) >> Either.right(new ExceptionManagementResult(dashboardId: MD_DASHBOARD_ID,
    status: CalculationTestStatus.IN_OVERLAY,
    id: RESULT_ID,))

    TaskExecution taskExecution = Mock(TaskExecution)
    taskExecution.dashboardId >> MD_DASHBOARD_ID
    taskExecution.status >> TaskExecutionStatus.NOT_STARTED
    taskExecution.breaksCount >> 1L
    taskExecution.curveConfigurationId >> CURVE_CONFIGURATION.entityId
    taskExecution.curveConfigurationName >> CURVE_CONFIGURATION.name
    1 * taskExecutionService.createTaskExecutions(MARKET_DATA_DASHBOARD, _, STEP_STATE_DATE) >> [taskExecution]

    1 * processor.createMdSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMd>
      assert steps.size() == 1
      assert steps[0].status == StepStatus.IN_PROGRESS
      assert steps[0].breaksCount == 1L
      assert steps[0].curveConfiguration == CURVE_CONFIGURATION
      return Either.right(steps)
    }

    when:
    def result = initializer.execute(MARKET_DATA_DASHBOARD)

    then:
    result.isRight()
  }

  def "should create steps when tasks approved"() {
    setup:
    exceptionManagementRepository.entityByDashboard(MD_DASHBOARD_ID) >> Either.right(new ExceptionManagementResult(dashboardId: MD_DASHBOARD_ID,
    status: CalculationTestStatus.APPROVED,
    id: RESULT_ID,))

    TaskExecution taskExecution = Mock(TaskExecution)
    taskExecution.dashboardId >> MD_DASHBOARD_ID
    taskExecution.status >> TaskExecutionStatus.APPROVED
    taskExecution.breaksCount >> null
    taskExecution.curveConfigurationId >> CURVE_CONFIGURATION.entityId
    taskExecution.curveConfigurationName >> CURVE_CONFIGURATION.name
    1 * taskExecutionService.createTaskExecutions(MARKET_DATA_DASHBOARD, _, STEP_STATE_DATE) >> [taskExecution]

    1 * processor.createMdSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMd>
      assert steps.size() == 1
      assert steps[0].status == StepStatus.COMPLETED
      assert steps[0].breaksCount == 0L
      assert steps[0].curveConfiguration == CURVE_CONFIGURATION
      return Either.right(steps)
    }

    when:
    def result = initializer.execute(MARKET_DATA_DASHBOARD)

    then:
    result.isRight()
  }
}
