package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryrun

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.COMPLETED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.REQUESTED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.UPDATED

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.error.Error
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing.MdBatchPreliminaryClearingEvent
import io.atlassian.fugue.Either
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class MdBatchPreliminaryRunEventListenerTest extends Specification {

  private static final RESULT_ID = "exceptionManagementResultId"

  DashboardRepository dashboardRepository = Mock()
  ApplicationEventPublisher publisher = Mock()
  MdBatchPreliminaryRunExecutor runExecutor = Mock()
  MdBatchPreliminaryRunCleanup stepCleanup = Mock()

  MdBatchPreliminaryRunEventListener listener = new MdBatchPreliminaryRunEventListener(
  dashboardRepository,
  publisher,
  runExecutor, stepCleanup
  )

  def setup() {
    dashboardRepository.dashboard(MD_BATCH_DASHBOARD_ID) >> Either.right(MD_BATCH_DASHBOARD)
  }

  def "should correctly process MD_BATCH_PRELIMINARY_RUN step event when REQUESTED"() {
    when:
    listener.onEvent(MdBatchPreliminaryRunEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * runExecutor.execute(MD_BATCH_DASHBOARD) >> Either.right(EntityId.entityId(RESULT_ID))
    1 * publisher.publishEvent(MdBatchPreliminaryRunEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(COMPLETED)
      .build()
      )
  }

  def "should correctly process MD_BATCH_PRELIMINARY_RUN step event when UPDATED"() {
    when:
    listener.onEvent(MdBatchPreliminaryRunEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(UPDATED)
      .build())

    then:
    0 * runExecutor._
    0 * publisher._
  }

  def "should correctly process MD_BATCH_PRELIMINARY_RUN step event when COMPLETED"() {
    when:
    listener.onEvent(MdBatchPreliminaryRunEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    then:
    1 * publisher.publishEvent(MdBatchPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(REQUESTED)
      .build())
  }

  def "should correctly process COMPLETED MD_BATCH_PRELIMINARY_RUN step event when dashboard deleted"() {
    setup:
    def dashboardId = "DELETED"
    1 * dashboardRepository.dashboard(dashboardId) >> Either.left(Error.OBJECT_NOT_FOUND.entity())

    when:
    listener.onEvent(MdBatchPreliminaryRunEvent
      .builder()
      .dashboardId(dashboardId)
      .type(COMPLETED)
      .build())

    then:
    0 * publisher._
    1 * stepCleanup.execute(dashboardId)
  }
}
