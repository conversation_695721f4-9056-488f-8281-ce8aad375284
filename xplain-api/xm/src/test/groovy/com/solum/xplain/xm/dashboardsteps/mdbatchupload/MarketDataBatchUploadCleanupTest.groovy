package com.solum.xplain.xm.dashboardsteps.mdbatchupload


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID

import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import spock.lang.Specification

class MarketDataBatchUploadCleanupTest extends Specification {
  DashboardEntryRepository entryRepository = Mock()
  MarketDataBatchUploadCleanup stepCleanup = new MarketDataBatchUploadCleanup(entryRepository)

  def "on execution should invoke repository"() {
    when:
    stepCleanup.execute(MD_BATCH_DASHBOARD_ID)

    then:
    1 * entryRepository.deleteMdBatchEntries(MD_BATCH_DASHBOARD_ID, DashboardStep.MARKET_DATA_BATCH_UPLOAD)
  }
}
