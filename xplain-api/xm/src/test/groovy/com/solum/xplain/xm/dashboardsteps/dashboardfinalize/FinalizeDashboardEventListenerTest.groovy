package com.solum.xplain.xm.dashboardsteps.dashboardfinalize


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VD_DASHBOARD_ID

import com.solum.xplain.core.error.Error
import com.solum.xplain.xm.dashboards.entity.DashboardBuilder
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import io.atlassian.fugue.Either
import spock.lang.Specification

class FinalizeDashboardEventListenerTest extends Specification {
  DashboardRepository dashboardRepository = Mock()
  DashboardEntryRepository entryRepository = Mock()

  FinalizeDashboardEventListener listener = new FinalizeDashboardEventListener(dashboardRepository, entryRepository)

  def "should finalize MD dashboard"() {
    when:
    listener.onEvent(new FinalizeDashboardEvent(MD_DASHBOARD_ID))

    then:
    1 * dashboardRepository.dashboard(MD_DASHBOARD_ID) >> Either.right(DashboardBuilder.MARKET_DATA_DASHBOARD)
    1 * entryRepository.getMdEntries(MD_DASHBOARD_ID) >> mdEntries
    callFinishCount * dashboardRepository.markFinished(MD_DASHBOARD_ID)

    where:
    mdEntries                                               | callFinishCount
    [new DashboardEntryMd(status: StepStatus.NOT_REQUIRED)] | 1
    [new DashboardEntryMd(status: StepStatus.COMPLETED)]    | 1
    [new DashboardEntryMd(status: StepStatus.IN_PROGRESS)]  | 0
  }

  def "should finalize MD BATCH dashboard"() {
    when:
    listener.onEvent(new FinalizeDashboardEvent(MD_BATCH_DASHBOARD_ID))

    then:
    1 * dashboardRepository.dashboard(MD_BATCH_DASHBOARD_ID) >> Either.right(DashboardBuilder.MD_BATCH_DASHBOARD)
    1 * entryRepository.getMdBatchEntries(MD_BATCH_DASHBOARD_ID) >> mdEntries
    callFinishCount * dashboardRepository.markFinished(MD_BATCH_DASHBOARD_ID)

    where:
    mdEntries                                               | callFinishCount
    [new DashboardEntryMd(status: StepStatus.NOT_REQUIRED)] | 1
    [new DashboardEntryMd(status: StepStatus.COMPLETED)]    | 1
    [new DashboardEntryMd(status: StepStatus.IN_PROGRESS)]  | 0
  }

  def "should finalize VD dashboard"() {
    when:
    listener.onEvent(new FinalizeDashboardEvent(VD_DASHBOARD_ID))

    then:
    1 * dashboardRepository.dashboard(VD_DASHBOARD_ID) >> Either.right(DashboardBuilder.valuationDataDashboard())
    1 * dashboardRepository.markFinished(VD_DASHBOARD_ID)
  }

  def "should handle when dashboard not found"() {
    when:
    listener.onEvent(new FinalizeDashboardEvent(MD_BATCH_DASHBOARD_ID))

    then:
    1 * dashboardRepository.dashboard(MD_BATCH_DASHBOARD_ID) >> Either.left(Error.OBJECT_NOT_FOUND.entity())
    0 * entryRepository.getMdEntries(_)
    0 * dashboardRepository.markFinished(_)
  }
}
