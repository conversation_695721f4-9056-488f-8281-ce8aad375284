package com.solum.xplain.xm.dashboardsteps.mdupload


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.marketDataDashboard

import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor
import io.atlassian.fugue.Either
import spock.lang.Specification

class MarketDataUploadExecutorTest extends Specification {

  DashboardStepProcessor processor = Mock()

  MarketDataUploadExecutor executor = new MarketDataUploadExecutor(processor)

  def "should execute"() {
    setup:
    def dashboard = marketDataDashboard()

    1 * processor.createMdSteps(_ as List<DashboardEntryMd>) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMd>
      assert steps.size() == 1
      assert steps[0].step == DashboardStep.MARKET_DATA_UPLOAD
      assert steps[0].status == StepStatus.COMPLETED
      assert steps[0].startedAt != null
      assert steps[0].finishedAt != null
      return Either.right(steps)
    }

    when:
    def result = executor.execute(dashboard)

    then:
    result.isRight()
    result.getOrNull() == dashboard.entityId()
  }
}
