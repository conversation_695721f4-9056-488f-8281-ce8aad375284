package com.solum.xplain.xm.dashboardsteps

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import io.atlassian.fugue.Either
import spock.lang.Specification

class DashboardStepProcessorTest extends Specification {

  private static final BitemporalDate STEP_STATE_DATE = BitemporalDate.newOf(STATE_DATE)

  DashboardEntryRepository dashboardEntryRepository = Mock()
  AuditEntryService auditEntryService = Mock()

  DashboardStepProcessor processor = new DashboardStepProcessor(dashboardEntryRepository, auditEntryService)

  def "should log failed step"() {
    setup:
    def step = new DashboardEntryMd(step: DashboardStep.MD_PRELIMINARY_CLEARING)
    def error = Error.OPERATION_NOT_ALLOWED.entity(errorMessage)

    when:
    def result = processor.performMdStep(STEP_STATE_DATE, step, { -> Either.left(error) })

    then:
    1 * dashboardEntryRepository.createEntry( {
      it.isSameEntry(step) && it.status == StepStatus.IN_PROGRESS && it.startedAt == STEP_STATE_DATE.recordDate
    }
    ) >> Either.right(step)

    1 * dashboardEntryRepository.updateEntry( {
      it.isSameEntry(step) && it.status == StepStatus.FAILED && it.finishedAt != null && it.error == error
    }
    ) >> Either.right(step)

    1 * auditEntryService.newEntryWithLogs(_, [error])

    result.isLeft()
    def errors = result.left().getOrNull() as List<ErrorItem>
    errors.size() == 1
    errors[0] == error

    where:
    errorMessage << ["failed", null]
  }
}
