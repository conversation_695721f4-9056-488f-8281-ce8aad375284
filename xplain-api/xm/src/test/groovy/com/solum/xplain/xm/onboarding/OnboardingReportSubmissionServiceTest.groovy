package com.solum.xplain.xm.onboarding

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.FAILED
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.NOT_REQUIRED
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.SUCCESS
import static com.solum.xplain.xm.onboarding.entity.OnboardingReportStatus.IN_PROGRESS
import static com.solum.xplain.xm.onboarding.entity.OnboardingReportStatus.READY_FOR_SUBMISSION
import static com.solum.xplain.xm.onboarding.view.SubmissionResult.SUBMITTED
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.portfolio.repository.PortfolioItemWriteRepository
import com.solum.xplain.core.portfolio.value.OnboardingVerificationResult
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.onboarding.entity.OnboardingReport
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItem
import org.bson.types.ObjectId
import spock.lang.Specification

class OnboardingReportSubmissionServiceTest extends Specification {

  def portfolioItemWriteRepository = Mock(PortfolioItemWriteRepository)
  def onboardingReportRepository = Mock(OnboardingReportRepository)
  def auditEntryService = Mock(AuditEntryService)

  def service = new OnboardingReportSubmissionService(
  portfolioItemWriteRepository,
  onboardingReportRepository,
  auditEntryService,
  )

  def "should correctly submit report"() {
    setup:
    def reportId = ObjectId.get()
    def reportItemId = ObjectId.get()
    1 * onboardingReportRepository.findReportById(reportId) >> right(new OnboardingReport(id: reportId, status: READY_FOR_SUBMISSION))
    1 * onboardingReportRepository.verifiedReportItems(reportId) >> [
      new OnboardingReportItem(
      id: reportItemId,
      trade: new Trade(portfolioId: "PID", entityId: "EID"),
      xplainCheckStatus: SUCCESS,
      marketCheckStatus: NOT_REQUIRED,
      vendorCheckStatus: FAILED,
      )
    ]

    1 * portfolioItemWriteRepository.updateConformityChecks("PID", "EID", null, null, new OnboardingVerificationResult(true, false, false), _) >> right(EntityId.entityId("EID"))

    when:
    def result = service.submitReport(reportId)

    then:
    result.isRight()
    result.getOrNull() == SUBMITTED
    1 * onboardingReportRepository.updateSubmissionStatus(reportId, [reportItemId], [])
  }

  def "should fail to submit report when not ready for submission"() {
    setup:
    def reportId = ObjectId.get()
    1 * onboardingReportRepository.findReportById(reportId) >> right(new OnboardingReport(id: reportId, status: IN_PROGRESS))

    when:
    def result = service.submitReport(reportId)

    then:
    result.isLeft()
    result.left().get() == OPERATION_NOT_ALLOWED.entity("Report is not ready for submission")
    0 * onboardingReportRepository.updateSubmissionStatus(_, _, _)
  }

  def "should fail to submit report when no items to submit"() {
    setup:
    def reportId = ObjectId.get()
    1 * onboardingReportRepository.findReportById(reportId) >> right(new OnboardingReport(id: reportId, status: READY_FOR_SUBMISSION))
    1 * onboardingReportRepository.verifiedReportItems(reportId) >> []

    when:
    def result = service.submitReport(reportId)

    then:
    result.isRight()
    result.getOrNull() == SUBMITTED
    1 * onboardingReportRepository.updateSubmissionStatus(reportId, [], [])
  }
}
