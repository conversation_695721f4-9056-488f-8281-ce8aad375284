package com.solum.xplain.xm.onboarding.breaks

import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.PRIMARY
import static com.solum.xplain.xm.excmngmt.rules.value.Operator.GT
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.ABSOLUTE_DIFF
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.NAV
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.NOTIONAL
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType.RELATIVE_DIFF
import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.AC_P1
import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.DC_XPLAIN

import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.rulesonboarding.OnboardingBreakTest
import com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType
import com.solum.xplain.xm.onboarding.entity.OnboardingTradeResultBreak
import spock.lang.Specification

class OnboardingBreakTestCalculationsTest extends Specification {

  def "should correctly skip tests that do not match trade"() {
    setup:
    def trade = Mock(Trade)
    def bt = breakTest()
    1 * bt.matches(trade) >> false
    def calculation = new OnboardingBreakTestCalculations([bt])

    when:
    def result = calculation.process(trade, new OnboardingBreakTestData(pv: 0))

    then:
    result == []
  }

  def "should correctly construct result when no data"() {
    setup:
    def trade = Mock(Trade)
    1 * trade.getNotional() >> 1.0
    def bt = breakTest()
    1 * bt.matches(trade) >> true
    1 * bt.resolveFirstThreshold(trade) >> 1
    def calculation = new OnboardingBreakTestCalculations([bt])

    when:
    def result = calculation.process(trade, new OnboardingBreakTestData())

    then:
    result == [
      new OnboardingTradeResultBreak(
      breakTestName: "BT",
      breakTestType: DC_XPLAIN,
      measureType: ABSOLUTE_DIFF,
      operator: GT,
      threshold: 1,
      value: null,
      triggered: true
      )
    ]
  }

  def "should correctly construct result with data and two tests"() {
    setup:
    def trade = Mock(Trade)
    1 * trade.getDealCost() >> 1.5
    1 * trade.getAccountingCost() >> 1.6
    2 * trade.getNotional() >> 1.7

    def bt1 = breakTest()
    1 * bt1.matches(trade) >> true
    1 * bt1.resolveFirstThreshold(trade) >> 1
    def bt2 = breakTest(AC_P1)
    1 * bt2.matches(trade) >> true
    1 * bt2.resolveFirstThreshold(trade) >> 2

    def calculation = new OnboardingBreakTestCalculations([bt1, bt2])

    when:
    def result = calculation.process(trade, new OnboardingBreakTestData(pv: 0))

    then:
    result == [
      new OnboardingTradeResultBreak(
      breakTestName: "BT",
      breakTestType: DC_XPLAIN,
      measureType: ABSOLUTE_DIFF,
      operator: GT,
      threshold: 1,
      value: 1.5,
      triggered: true
      ),
      new OnboardingTradeResultBreak(
      breakTestName: "BT",
      breakTestType: AC_P1,
      measureType: ABSOLUTE_DIFF,
      operator: GT,
      threshold: 2,
      value: 1.6,
      triggered: false
      )
    ]
  }

  def "should correctly construct result with RELATIVE DIFF calculation"() {
    setup:
    def trade = Mock(Trade)
    1 * trade.getDealCost() >> 1.5
    1 * trade.getNotional() >> 1.0

    def bt = Mock(OnboardingBreakTest)
    bt.getName() >> "BT"
    bt.getType() >> DC_XPLAIN
    bt.getMeasureType() >> RELATIVE_DIFF
    bt.getOperator() >> GT
    1 * bt.matches(trade) >> true
    1 * bt.resolveFirstThreshold(trade) >> 2


    def calculation = new OnboardingBreakTestCalculations([bt])

    when:
    def result = calculation.process(trade, new OnboardingBreakTestData(pv: 3), PRIMARY)

    then:
    result == [
      new OnboardingTradeResultBreak(
      breakTestName: "BT",
      breakTestType: DC_XPLAIN,
      providerType: PRIMARY,
      measureType: RELATIVE_DIFF,
      operator: GT,
      threshold: 2,
      value: 1.000000,
      triggered: false
      ),
    ]
  }

  def "should correctly construct result with NAV/NOTIONAL calculation"() {
    setup:
    def trade = Mock(Trade)
    2 * trade.getDealCost() >> 1.5
    2 * trade.getNotional() >> 2.0

    def btNotional = Mock(OnboardingBreakTest)
    btNotional.getName() >> "BTNOTIONAL"
    btNotional.getType() >> DC_XPLAIN
    btNotional.getMeasureType() >> NOTIONAL
    btNotional.getOperator() >> GT
    1 * btNotional.matches(trade) >> true
    1 * btNotional.resolveFirstThreshold(trade) >> 2
    def btNav = Mock(OnboardingBreakTest)
    btNav.getName() >> "BTNAV"
    btNav.getType() >> DC_XPLAIN
    btNav.getMeasureType() >> NAV
    btNav.getOperator() >> GT
    1 * btNav.matches(trade) >> true
    1 * btNav.resolveFirstThreshold(trade) >> 2

    def calculation = new OnboardingBreakTestCalculations([btNotional, btNav])

    when:
    def result = calculation.process(trade, new OnboardingBreakTestData(pv: 3, nav: 5), PRIMARY)

    then:
    result == [
      new OnboardingTradeResultBreak(
      breakTestName: "BTNOTIONAL",
      breakTestType: DC_XPLAIN,
      providerType: PRIMARY,
      measureType: NOTIONAL,
      operator: GT,
      threshold: 2,
      value: 7500.000000,
      triggered: true
      ),
      new OnboardingTradeResultBreak(
      breakTestName: "BTNAV",
      breakTestType: DC_XPLAIN,
      providerType: PRIMARY,
      measureType: NAV,
      operator: GT,
      threshold: 2,
      value: 3000.000000,
      triggered: true
      ),
    ]
  }

  OnboardingBreakTest breakTest(OnboardingTestType type = DC_XPLAIN) {
    def bt = Mock(OnboardingBreakTest)
    bt.getName() >> "BT"
    bt.getType() >> type
    bt.getMeasureType() >> ABSOLUTE_DIFF
    bt.getOperator() >> GT
    bt
  }
}
