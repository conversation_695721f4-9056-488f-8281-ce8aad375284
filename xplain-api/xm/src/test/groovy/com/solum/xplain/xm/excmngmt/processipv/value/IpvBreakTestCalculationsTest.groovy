package com.solum.xplain.xm.excmngmt.processipv.value

import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak
import com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType
import java.time.LocalDate
import spock.lang.Specification

class IpvBreakTestCalculationsTest extends Specification {

  def VALUATION_DATE = LocalDate.now()

  def "should filter calculations based on applicability to trade"() {
    given:
    def mockCalculation = Mock(IpvBreakTestCalculation)
    def trade = Mock(Trade)
    mockCalculation.isRelevant() >> true
    mockCalculation.isApplicable(trade) >> applicable

    IpvBreakTestCalculations ipvBreakTestCalculations = new IpvBreakTestCalculations([mockCalculation])

    when:
    def result = ipvBreakTestCalculations.filteredFor(trade)

    then:
    !ipvBreakTestCalculations.isEmpty()
    result.isEmpty() == empty

    where:
    applicable  || empty
    false       || true
    true        || false
  }

  def "should filter calculations based on relevance"() {
    given:
    def mockCalculation1 = Mock(IpvBreakTestCalculation)
    def mockCalculation2 = Mock(IpvBreakTestCalculation)
    mockCalculation1.isRelevant() >> relevant1
    mockCalculation2.isRelevant() >> relevant2

    IpvBreakTestCalculations ipvBreakTestCalculations = new IpvBreakTestCalculations([mockCalculation1, mockCalculation2])

    expect:
    ipvBreakTestCalculations.isEmpty() == isEmpty

    where:
    relevant1 | relevant2 || isEmpty
    false     | false     || true
    false     | true      || false
    true      | false     || false
    true      | true      || false
  }

  def "should preserve break test order when processing calculations"() {
    given:
    def tradeCalculator = Mock(TradeBreakCalculator)
    def mockCalculation1 = Mock(IpvBreakTestCalculation, {
      resolveBreak(tradeCalculator, VALUATION_DATE) >> [
        new TradeResultBreak(breakTestId: "bish", sequence: 1, providerType: IpvBreakProviderType.PRIMARY),
        new TradeResultBreak(breakTestId: "bish", sequence: 1, providerType: IpvBreakProviderType.SECONDARY)
      ]
    })
    def mockCalculation2 = Mock(IpvBreakTestCalculation,  {
      resolveBreak(tradeCalculator, VALUATION_DATE) >> [
        new TradeResultBreak(breakTestId: "bash", sequence: 2, providerType: IpvBreakProviderType.PRIMARY),
        new TradeResultBreak(breakTestId: "bash", sequence: 2, providerType: IpvBreakProviderType.SECONDARY)
      ]
    })
    def mockCalculation3 = Mock(IpvBreakTestCalculation,  {
      resolveBreak(tradeCalculator, VALUATION_DATE) >> [new TradeResultBreak(breakTestId: "bosh", sequence: 3, providerType: IpvBreakProviderType.PRIMARY)]
    })
    IpvBreakTestCalculations ipvBreakTestCalculations = new IpvBreakTestCalculations([mockCalculation1, mockCalculation2, mockCalculation3])

    when:
    def result = ipvBreakTestCalculations.processCalc(tradeCalculator, VALUATION_DATE)

    then:
    result*.breakTestId == ["bish", "bish", "bash", "bash", "bosh"]
  }

  def "should preserve break test order when child tests are processed"() {
    given:
    def tradeCalculator = Mock(TradeBreakCalculator)
    def mockCalculation1 = Mock(IpvBreakTestCalculation, {
      resolveBreak(tradeCalculator, VALUATION_DATE) >> [
        new TradeResultBreak(breakTestId: "bish", sequence: 2, providerType: IpvBreakProviderType.PRIMARY),
        new TradeResultBreak(breakTestId: "bish-child", sequence: 5, providerType: IpvBreakProviderType.PRIMARY)
      ]
    })
    def mockCalculation2 = Mock(IpvBreakTestCalculation, {
      resolveBreak(tradeCalculator, VALUATION_DATE) >> [
        new TradeResultBreak(breakTestId: "bash", sequence: null, providerType: IpvBreakProviderType.PRIMARY),
        new TradeResultBreak(breakTestId: "bash-child", sequence: 4, providerType: IpvBreakProviderType.PRIMARY)
      ]
    })
    def mockCalculation3 = Mock(IpvBreakTestCalculation,  {
      resolveBreak(tradeCalculator, VALUATION_DATE) >> [new TradeResultBreak(breakTestId: "bosh", sequence: 3, providerType: IpvBreakProviderType.PRIMARY)]
    })
    IpvBreakTestCalculations ipvBreakTestCalculations = new IpvBreakTestCalculations([mockCalculation1, mockCalculation2, mockCalculation3])

    when:
    def result = ipvBreakTestCalculations.processCalc(tradeCalculator, VALUATION_DATE)

    then:
    result*.breakTestId == ["bash", "bish", "bosh", "bash-child", "bish-child"]
  }
}
