package com.solum.xplain.xm.excmngmt.processipv

import static groovy.json.JsonOutput.toJson
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedView
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.xm.excmngmt.processipv.form.ApplyResolutionForm
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import com.solum.xplain.xm.tasks.repository.IpvTaskExecutionRepository
import com.solum.xplain.xm.workflow.view.CallActivityProgressView
import java.nio.charset.StandardCharsets
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.core.Authentication
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@MockMvcConfiguration
@WebMvcTest(controllers = [IpvExceptionManagementController])
class IpvExceptionManagementControllerTest extends Specification {

  @SpringBean
  IpvExceptionManagementControllerService service = Mock()
  @SpringBean
  IpvExceptionManagementGraphsService graphsService = Mock()
  @SpringBean
  AuthenticationContext userRepository = Mock()
  @SpringBean
  IpvTaskExecutionRepository executionRepository = Mock()
  @SpringBean
  IpvDataGroupRepository ipvDataGroupRepository = Mock()
  @SpringBean
  AuditEntryService auditEntryService = Mock()

  @Autowired
  ObjectMapper objectMapper
  @Autowired
  MockMvc mockMvc

  def "should return workflow progress"() {
    setup:
    1 * service.overlayProgress("key") >> new CallActivityProgressView(totalCount: 10)

    when:
    def results = mockMvc.perform(get("/exception-management/ipv-calculations/overlay/progress?key=key"))
      .andReturn()

    then:
    with (results.getResponse()) {
      getStatus() == 200
      with (objectMapper.readValue(getContentAsByteArray(), CallActivityProgressView)) {
        totalCount == 10
      }
    }
  }

  @WithMockUser
  @Unroll
  def "should perform workflow resolution form (#form) validation with response #code #responseBody"() {
    setup:
    (code == 200 ? 1 : 0) * service.applyResolution(_ as Authentication, _ as ApplyResolutionForm, _ as TableFilter, null) >> right(EntityId.entityId("1"))
    ipvDataGroupRepository.ipvDataGroupCondensedView(_ as String) >> Optional.of(new IpvDataGroupCondensedView())
    userRepository.userEither(_ as Authentication) >> right(UserBuilder.user())
    def results = mockMvc.perform(multipart("/exception-management/ipv-calculations/overlay/resolution?filter=tradeInfoTradeTypeGroup,equal,FX&filter=type,equal,FXOPT&filter=currencyPair,equal,USD/CAD")
      .file(jsonForm(toJson(form)))
      .with(csrf()))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                                                            | responseBody                                        | code
    formOverall({ m -> m })                                                                                                         | "id"                                                | 200
    formOverall({ m -> m.replace("overallResolution", [resolutionType: "KEEP", comment: "CCC"])})                                   | "id"                                                | 200
    formOverall({ m -> m.replace("overallResolution", [resolutionType: "HOLD", resolutionSubType: {name: "SUB"}, comment: "CCC"])}) | "id"                                                | 200
    formOverall({ m -> m.remove("overallResolution") })                                                                             | "NotNull.form.overallResolution"                    | 412
    formOverall({ m -> m.replace("overallResolution", [newValue: "1", comment: "CCC"]) })                                           | "NotNull.form.overallResolution.resolutionType"     | 412
    formOverall({ m -> m.replace("overallResolution", [resolutionType: "OVERRIDE_USER", comment: "CCC"]) })                         | "NotNull.form.overallResolution.newValue"           | 412
    formOverall({ m -> m.replace("overallResolution", [newValue: "1", resolutionType: "OVERRIDE_USER"]) })                          | "NotEmpty.form.overallResolution.comment"           | 412
    formOverall({ m -> m.replace("overallResolution", [resolutionType: "HOLD", comment: "CCC"]) })                                  | "NotNull.form.overallResolution.resolutionSubType"  | 412
  }

  private MockMultipartFile jsonForm(String json) {
    return new MockMultipartFile("form", "", MediaType.APPLICATION_JSON_VALUE, json.getBytes(StandardCharsets.UTF_8))
  }

  static def form(Closure c) {
    [
      valuationDate: "2020-01-02",
      ipvDataGroup : "groupId",
      portfolioIds : []
    ].with(true, c)
  }

  static def formOverall(Closure c) {
    [
      taskIds          : ["id"],
      selectedIds      : [],
      overallResolution: [
        resolutionType: "OVERRIDE_USER",
        newValue      : "1",
        comment       : "CCC"
      ]
    ].with(true, c)
  }
}
