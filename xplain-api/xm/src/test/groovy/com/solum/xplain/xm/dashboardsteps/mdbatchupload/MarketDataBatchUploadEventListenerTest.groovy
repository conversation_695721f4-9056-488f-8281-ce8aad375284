package com.solum.xplain.xm.dashboardsteps.mdbatchupload

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.COMPLETED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.REQUESTED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.UPDATED

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.error.Error
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryrun.MdBatchPreliminaryRunEvent
import io.atlassian.fugue.Either
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class MarketDataBatchUploadEventListenerTest extends Specification {

  DashboardRepository dashboardRepository = Mock()
  ApplicationEventPublisher publisher = Mock()
  MarketDataBatchUploadExecutor uploadExecutor = Mock()
  MarketDataBatchUploadCleanup stepCleanup = Mock()

  MarketDataBatchUploadEventListener listener = new MarketDataBatchUploadEventListener(
  dashboardRepository,
  publisher,
  uploadExecutor,
  stepCleanup,
  )

  def setup() {
    dashboardRepository.dashboard(MD_BATCH_DASHBOARD_ID) >> Either.right(MD_BATCH_DASHBOARD)
  }

  def "should correctly process MARKET_DATA_BATCH_UPLOAD step event when REQUESTED"() {
    when:
    listener.onEvent(MarketDataBatchUploadEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * uploadExecutor.execute(MD_BATCH_DASHBOARD) >> Either.right(EntityId.entityId(MD_BATCH_DASHBOARD_ID))
    1 * publisher.publishEvent(MarketDataBatchUploadEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(COMPLETED)
      .build())
  }

  def "should correctly process MARKET_DATA_BATCH_UPLOAD step event when REQUESTED but fails"() {
    when:
    listener.onEvent(MarketDataBatchUploadEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * uploadExecutor.execute(MD_BATCH_DASHBOARD) >> Either.left(
      Error.OPERATION_NOT_ALLOWED.entity()
      )
    0 * publisher._
  }

  def "should correctly process MARKET_DATA_BATCH_UPLOAD step event when UPDATED"() {
    when:
    listener.onEvent(MarketDataBatchUploadEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(UPDATED)
      .build())

    then:
    0 * uploadExecutor._
    0 * publisher._
  }

  def "should correctly process MARKET_DATA_BATCH_UPLOAD step event when COMPLETED"() {
    when:
    listener.onEvent(MarketDataBatchUploadEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    then:
    1 * publisher.publishEvent(MdBatchPreliminaryRunEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(REQUESTED)
      .build())
  }

  def "should correctly process MARKET_DATA_BATCH_UPLOAD step event when COMPLETED and dashboard delted"() {
    when:
    dashboardRepository.dashboard(MD_DASHBOARD_ID) >> Either.left(Error.OBJECT_NOT_FOUND.entity())
    listener.onEvent(MarketDataBatchUploadEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    then:
    0 * publisher._
    1 * stepCleanup.execute(MD_DASHBOARD_ID)
  }
}
