package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID

import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository
import spock.lang.Specification

class MdOverlayClearingCleanupTest extends Specification {
  TaskExecutionRepository repository = Mock()
  DashboardEntryRepository entryRepository = Mock()
  MdOverlayClearingCleanup stepCleanup = new MdOverlayClearingCleanup(repository, entryRepository)

  def "on execution should invoke repository"() {
    when:
    stepCleanup.execute(MD_DASHBOARD_ID)

    then:
    1 * repository.overlayDeleted(MD_DASHBOARD_ID)
    1 * entryRepository.deleteMdEntries(MD_DASHBOARD_ID, DashboardStep.MD_OVERLAY_CLEARING)
  }
}
