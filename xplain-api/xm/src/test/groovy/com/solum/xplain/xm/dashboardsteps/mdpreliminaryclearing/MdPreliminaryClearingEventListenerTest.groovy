package com.solum.xplain.xm.dashboardsteps.mdpreliminaryclearing


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdOverlayClearing
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdPreliminaryClearing
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.COMPLETED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.REQUESTED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.UPDATED

import com.solum.xplain.core.error.Error
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboardsteps.mdoverlayrun.MdOverlayRunEvent
import io.atlassian.fugue.Either
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class MdPreliminaryClearingEventListenerTest extends Specification {

  DashboardRepository dashboardRepository = Mock()
  ApplicationEventPublisher publisher = Mock()
  MdPreliminaryClearingStepsInitializer clearingStepsInitializer = Mock()
  MdPreliminaryClearingStepsFinalizer clearingStepsFinalizer = Mock()
  MdPreliminaryClearingCleanup stepCleanup = Mock()

  MdPreliminaryClearingEventListener listener = new MdPreliminaryClearingEventListener(dashboardRepository,
  publisher,
  clearingStepsInitializer,
  clearingStepsFinalizer,
  stepCleanup)

  def setup() {
    dashboardRepository.dashboard(MD_DASHBOARD_ID) >> Either.right(MARKET_DATA_DASHBOARD)
  }

  def "should correctly process MD_PRELIMINARY_CLEARING step event"() {
    when: "REQUESTED - initialize clearing steps with all steps completed"
    listener.onEvent(MdPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * clearingStepsInitializer.execute(MARKET_DATA_DASHBOARD) >> Either.right([dashboardStepMdPreliminaryClearing()])
    1 * publisher.publishEvent(MdPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    when: "REQUESTED - initialize clearing steps with not all steps completed"
    listener.onEvent(MdPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * clearingStepsInitializer.execute(MARKET_DATA_DASHBOARD) >> Either.right([dashboardStepMdPreliminaryClearing().tap {
        status = StepStatus.IN_PROGRESS
      }])
    0 * publisher.publishEvent(_)

    when: "UPDATED - finalize clearing steps with all steps completed"
    listener.onEvent(MdPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .build())

    then:
    1 * clearingStepsFinalizer.execute(MARKET_DATA_DASHBOARD) >> Either.right([dashboardStepMdPreliminaryClearing()])
    1 * publisher.publishEvent(MdPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    when: "UPDATED - finalize clearing steps with not all steps completed"
    listener.onEvent(MdPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .build())

    then:
    1 * clearingStepsFinalizer.execute(MARKET_DATA_DASHBOARD) >> Either.right([dashboardStepMdPreliminaryClearing().tap {
        status = StepStatus.IN_PROGRESS
      }])
    0 * publisher.publishEvent(_)


    when: "COMPLETED - request overlay run"
    listener.onEvent(MdPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    then:
    1 * publisher.publishEvent(MdOverlayRunEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())
  }

  def "should correctly process MD_PRELIMINARY_CLEARING step event when dashboard deleted"() {
    setup:
    def dashboardId = "someId"
    def dashboard = new Dashboard(id: "DELETED")
    1 * dashboardRepository.dashboard(dashboardId) >> Either.right(dashboard)
    1 * dashboardRepository.dashboard("DELETED") >> Either.left(Error.OBJECT_NOT_FOUND.entity())
    1 * clearingStepsInitializer.execute(dashboard) >> Either.right([dashboardStepMdOverlayClearing()])

    when:
    listener.onEvent(MdPreliminaryClearingEvent
      .builder()
      .dashboardId(dashboardId)
      .type(REQUESTED)
      .build())


    then:
    1 * stepCleanup.execute("DELETED")
  }
}
