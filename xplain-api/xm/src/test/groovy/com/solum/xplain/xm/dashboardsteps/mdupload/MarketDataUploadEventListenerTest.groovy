package com.solum.xplain.xm.dashboardsteps.mdupload


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.COMPLETED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.REQUESTED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.UPDATED

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.error.Error
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboardsteps.mdpreliminaryrun.MdPreliminaryRunEvent
import io.atlassian.fugue.Either
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class MarketDataUploadEventListenerTest extends Specification {

  DashboardRepository dashboardRepository = Mock()
  ApplicationEventPublisher publisher = Mock()
  MarketDataUploadExecutor uploadExecutor = Mock()
  MarketDataUploadCleanup stepCleanup = Mock()

  MarketDataUploadEventListener listener = new MarketDataUploadEventListener(
  dashboardRepository,
  publisher,
  uploadExecutor,
  stepCleanup
  )

  def setup() {
    dashboardRepository.dashboard(MD_DASHBOARD_ID) >> Either.right(MARKET_DATA_DASHBOARD)
  }

  def "should correctly process MARKET_DATA_UPLOAD step event when REQUESTED"() {
    when:
    listener.onEvent(MarketDataUploadEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * uploadExecutor.execute(MARKET_DATA_DASHBOARD) >> Either.right(EntityId.entityId(MD_DASHBOARD_ID))
    1 * publisher.publishEvent(MarketDataUploadEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build())
  }

  def "should correctly process MARKET_DATA_UPLOAD step event when REQUESTED but fails"() {
    when:
    listener.onEvent(MarketDataUploadEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * uploadExecutor.execute(MARKET_DATA_DASHBOARD) >> Either.left(
      Error.OPERATION_NOT_ALLOWED.entity()
      )
    0 * publisher._
  }

  def "should correctly process MARKET_DATA_UPLOAD step event when UPDATED"() {
    when:
    listener.onEvent(MarketDataUploadEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .build())

    then:
    0 * uploadExecutor._
    0 * publisher._
  }

  def "should correctly process MARKET_DATA_UPLOAD step event when COMPLETED"() {
    when:
    listener.onEvent(MarketDataUploadEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    then:
    1 * publisher.publishEvent(MdPreliminaryRunEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())
  }

  def "should correctly process MARKET_DATA_BATCH_UPLOAD step event when COMPLETED and dashboard deleted"() {
    when:
    dashboardRepository.dashboard(MD_DASHBOARD_ID) >> Either.left(Error.OBJECT_NOT_FOUND.entity())
    listener.onEvent(MarketDataUploadEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    then:
    0 * publisher._
    1 * stepCleanup.execute(MD_DASHBOARD_ID)
  }
}
