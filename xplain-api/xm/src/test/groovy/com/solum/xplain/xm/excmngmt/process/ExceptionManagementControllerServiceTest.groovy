package com.solum.xplain.xm.excmngmt.process

import static com.solum.xplain.core.users.UserBuilder.user
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslator
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory
import com.solum.xplain.shared.utils.filter.FilterOperation
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.value.WorkflowStatus
import com.solum.xplain.xm.excmngmt.enums.VerificationStatus
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidenceRepository
import com.solum.xplain.xm.excmngmt.form.ApplyVerificationForm
import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm
import com.solum.xplain.xm.excmngmt.form.VerificationForm
import com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus
import com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType
import com.solum.xplain.xm.excmngmt.process.form.ApplyTaskResolutionForm
import com.solum.xplain.xm.excmngmt.process.form.InstrumentFilterForm
import com.solum.xplain.xm.excmngmt.process.form.InstrumentResultBreaksFilterForm
import com.solum.xplain.xm.excmngmt.process.form.ResolutionForm
import com.solum.xplain.xm.excmngmt.process.view.BreakCountView
import com.solum.xplain.xm.excmngmt.process.view.ExceptionManagementCountedFiltersView
import com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView
import com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView
import com.solum.xplain.xm.excmngmt.process.view.chart.TaskChartData
import com.solum.xplain.xm.tasks.entity.TaskExecution
import com.solum.xplain.xm.tasks.entity.TasksDefinition
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus
import com.solum.xplain.xm.tasks.repository.TaskDefinitionRepository
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository
import com.solum.xplain.xm.workflow.XmWorkflowService
import com.solum.xplain.xm.workflow.repository.XmStepInstanceQueryRepository
import com.solum.xplain.xm.workflow.view.CallActivityProgressView
import java.util.stream.Stream
import org.springframework.data.domain.Sort
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.web.multipart.MultipartFile
import spock.lang.Specification
import spock.lang.Unroll

class ExceptionManagementControllerServiceTest extends Specification {
  def viewQueryTranslator = Mock(ViewQueryTranslator)
  def viewQueryTranslatorFactory = Mock(ViewQueryTranslatorFactory) {
    getTranslator(_ as Class<?>) >> viewQueryTranslator
  }
  def repository = Mock(ExceptionManagementCalculationRepository)
  def userRepository = Mock(AuthenticationContext)
  def taskExecutionRepository = Mock(TaskExecutionRepository)
  def taskDefinitionRepository = Mock(TaskDefinitionRepository)
  def exceptionManagementEvidenceRepository = Mock(ExceptionManagementEvidenceRepository)
  def xmStepInstanceQueryRepository = Mock(XmStepInstanceQueryRepository)
  def chartRepository = Mock(ExceptionManagementChartRepository)
  def xmWorkflowService = Mock(XmWorkflowService)

  def service = new ExceptionManagementControllerService(
  exceptionManagementEvidenceRepository,
  repository,
  taskExecutionRepository,
  taskDefinitionRepository,
  userRepository,
  viewQueryTranslatorFactory,
  xmStepInstanceQueryRepository,
  xmWorkflowService,
  chartRepository
  )

  def "should get preliminary items"() {
    setup:
    def filter = TableFilter.emptyTableFilter()
    def scroll = ScrollRequest.of(0, 10)
    def mappedFilter = new TableFilter([new SimpleFilterClause("mapped", FilterOperation.EQUAL, "true")])
    def mappedScroll = ScrollRequest.of(1, 10, Sort.by("mapped"))
    def p = Mock(InstrumentPreliminaryResultView)
    1 * repository.preliminaryItemsDaily(_, _, mappedFilter, mappedScroll) >> ScrollableEntry.of([p], mappedScroll)

    when:
    def result = service.preliminaryItems([], new ResultDisplayFilterForm(), filter, scroll)

    then:
    result.content == [p]
    1 * viewQueryTranslator.translate(filter) >> mappedFilter
    1 * viewQueryTranslator.translate(scroll) >> mappedScroll
  }


  def "should get overlay items"() {
    setup:
    def filter = TableFilter.emptyTableFilter()
    def scroll = ScrollRequest.of(0, 10)
    def mappedFilter = new TableFilter([new SimpleFilterClause("mapped", FilterOperation.EQUAL, "true")])
    def mappedScroll = ScrollRequest.of(1, 10, Sort.by("mapped"))
    def o = Mock(InstrumentOverlayResultView)
    1 * repository.overlayItems(_ as List<String>, _ as ResultDisplayFilterForm, mappedFilter, mappedScroll) >> ScrollableEntry.of([o], mappedScroll)

    when:
    def result = service.overlayItems([], new ResultDisplayFilterForm(), filter, scroll)

    then:
    result.content == [o]
    1 * viewQueryTranslator.translate(filter) >> mappedFilter
    1 * viewQueryTranslator.translate(scroll) >> mappedScroll
  }

  def "should query repository for preliminary charts"() {
    when:
    def result = service.preliminaryTaskCharts([])

    then:
    1 * chartRepository.preliminaryChartData([]) >> new TaskChartData([])

    and:
    result == new TaskChartData([])
  }

  def "should query repository for overlay charts"() {
    when:
    def result = service.overlayTaskCharts([])

    then:
    1 * chartRepository.overlayChartData([]) >> new TaskChartData([])

    and:
    result == new TaskChartData([])
  }

  def "should apply resolution"() {
    setup:
    def evidence = Mock(MultipartFile)
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)
    def form = new ApplyTaskResolutionForm(taskIds: ["id"], selectedIds: ["key"])

    1 * userRepository.userEither(_ as Authentication) >> right(user)
    1 * xmWorkflowService.filterMdPreliminaryBusinessKeysByStepStatusAndUser(["key"], _, user) >> Stream.empty()
    1 * repository.saveResult(_, _, _, _, new ExceptionManagementEvidence()) >> right(new EntityId("1"))
    1 * taskExecutionRepository.executions(_) >> [new TaskExecution(status: TaskExecutionStatus.IN_RESOLUTION, dashboardId: "id")]
    1 * exceptionManagementEvidenceRepository.saveFile(["id"], evidence) >> right(new ExceptionManagementEvidence())
    when:
    def result = service.applyResolution(auth, form, TableFilter.emptyTableFilter(), evidence, CalculationTestStatus.IN_PRELIMINARY)

    then:
    result == right(new EntityId("1"))
  }

  @Unroll
  def "should apply overall workflow resolution for preliminary based on user assignment"() {
    setup:
    def evidence = Mock(MultipartFile)
    def currentUser = user("currentUser")
    def tableFilter = TableFilter.emptyTableFilter()
    def mappedFilter = new TableFilter([new SimpleFilterClause("mapped", FilterOperation.EQUAL, "true")])
    def auth = new TestingAuthenticationToken(currentUser, null)
    def translator = Mock(ViewQueryTranslator)
    def preliminaryView1 = Mock(InstrumentPreliminaryResultView) {
      getId() >> "key1"
    }
    def preliminaryView2 = Mock(InstrumentPreliminaryResultView) {
      getId() >> "key2"
    }
    def form = new ApplyTaskResolutionForm(
      taskIds: ["id1", "id2"],
      selectedIds: [],
      overallResolution: new ResolutionForm(InstrumentResultResolutionType.IGNORE_NULL, null, null)
      )

    when:
    def result = service.applyResolution(auth, form, tableFilter, evidence, CalculationTestStatus.IN_PRELIMINARY)

    then:
    1 * userRepository.userEither(_ as Authentication) >> right(currentUser)
    0 * taskDefinitionRepository.getTasksDefinitions(_)
    1 * viewQueryTranslatorFactory.getTranslator(InstrumentPreliminaryResultView) >> translator
    1 * translator.translate(tableFilter) >> mappedFilter
    1 * repository.preliminaryItemsDaily(
      new ResultDisplayFilterForm(true, false, false),
      ["id1", "id2"],
      mappedFilter,
      _ as ScrollRequest
      ) >> ScrollableEntry.of([preliminaryView1, preliminaryView2], ScrollRequest.unconstrained())

    1 * xmWorkflowService.filterMdPreliminaryBusinessKeysByStepStatusAndUser(
      ["key1", "key2"],
      [WorkflowStatus.ACTIVE],
      currentUser
      ) >> {
        return Stream.of("key1")
      }

    1 * xmWorkflowService.getDashboardIds(["key1"]) >> ["dashboardId1"]
    1 * exceptionManagementEvidenceRepository.saveFile(["dashboardId1"], evidence) >> right(new ExceptionManagementEvidence())
    1 * xmWorkflowService.submitMdPreliminaryResolutionForm(["key1"], _)

    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key1"]
    }
  }

  @Unroll
  def "should apply overall workflow resolution for overlay based on user assignment"() {
    setup:
    def evidence = Mock(MultipartFile)
    def currentUser = user("currentUser")
    def tableFilter = TableFilter.emptyTableFilter()
    def mappedFilter = new TableFilter([new SimpleFilterClause("mapped", FilterOperation.EQUAL, "true")])
    def auth = new TestingAuthenticationToken(currentUser, null)
    def translator = Mock(ViewQueryTranslator)
    def overlayView1 = Mock(InstrumentOverlayResultView) {
      getId() >> "key1"
    }
    def overlayView2 = Mock(InstrumentOverlayResultView) {
      getId() >> "key2"
    }
    def form = new ApplyTaskResolutionForm(
      taskIds: ["id1", "id2"],
      selectedIds: [],
      overallResolution: new ResolutionForm(InstrumentResultResolutionType.IGNORE_NULL, null, null)
      )

    when:
    def result = service.applyResolution(auth, form, tableFilter, evidence, CalculationTestStatus.IN_OVERLAY)

    then:
    1 * userRepository.userEither(_ as Authentication) >> right(currentUser)
    0 * taskDefinitionRepository.getTasksDefinitions(_)
    1 * viewQueryTranslatorFactory.getTranslator(InstrumentOverlayResultView) >> translator
    1 * translator.translate(tableFilter) >> mappedFilter
    1 * repository.overlayItems(
      ["id1", "id2"],
      new ResultDisplayFilterForm(true, false, false),
      mappedFilter,
      _ as ScrollRequest
      ) >> ScrollableEntry.of([overlayView1, overlayView2], ScrollRequest.unconstrained())

    1 * xmWorkflowService.filterMdOverlayBusinessKeysByStepStatusAndUser(
      ["key1", "key2"],
      [WorkflowStatus.ACTIVE],
      currentUser
      ) >> {
        return Stream.of("key1")
      }

    1 * xmWorkflowService.getDashboardIds(["key1"]) >> ["dashboardId1"]
    1 * exceptionManagementEvidenceRepository.saveFile(["dashboardId1"], evidence) >> right(new ExceptionManagementEvidence())
    1 * xmWorkflowService.submitMdOverlayResolutionForm(["key1"], _)

    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key1"]
    }
  }

  @Unroll
  def "should apply overall workflow resolution #status"() {
    setup:
    def evidence = Mock(MultipartFile)
    def user = user("user")
    def tableFilter = TableFilter.emptyTableFilter()
    def mappedFilter = new TableFilter([new SimpleFilterClause("mapped", FilterOperation.EQUAL, "true")])
    def auth = new TestingAuthenticationToken(user, null)
    def translator = Mock(ViewQueryTranslator)
    def preliminaryView = Mock(InstrumentPreliminaryResultView) {
      getId() >> "key2"
    }
    def overlayView = Mock(InstrumentOverlayResultView) {
      getId() >> "key2"
    }
    def form = new ApplyTaskResolutionForm(
      taskIds: ["id"],
      selectedIds: [],
      overallResolution: new ResolutionForm(InstrumentResultResolutionType.IGNORE_NULL, null, null)
      )

    when:
    def result = service.applyResolution(auth, form, tableFilter, evidence, status)

    then:
    1 * userRepository.userEither(_ as Authentication) >> right(user)
    0 * taskDefinitionRepository.getTasksDefinitions(_)

    if (status == CalculationTestStatus.IN_PRELIMINARY) {
      1 * viewQueryTranslatorFactory.getTranslator(InstrumentPreliminaryResultView) >> translator
      1 * translator.translate(tableFilter) >> mappedFilter
      1 * repository.preliminaryItemsDaily(
        new ResultDisplayFilterForm(true, false, false),
        ["id"],
        mappedFilter,
        _ as ScrollRequest
        ) >> ScrollableEntry.of([preliminaryView], ScrollRequest.unconstrained())
    } else {
      1 * viewQueryTranslatorFactory.getTranslator(InstrumentOverlayResultView) >> translator
      1 * translator.translate(tableFilter) >> mappedFilter
      1 * repository.overlayItems(
        ["id"],
        new ResultDisplayFilterForm(true, false, false),
        mappedFilter,
        _ as ScrollRequest
        ) >> ScrollableEntry.of([overlayView], ScrollRequest.unconstrained())
    }

    (status == CalculationTestStatus.IN_PRELIMINARY ? 1 : 0) * xmWorkflowService.filterMdPreliminaryBusinessKeysByStepStatusAndUser(["key2"], [WorkflowStatus.ACTIVE], user) >> Stream.of("key2")
    (status == CalculationTestStatus.IN_OVERLAY ? 1 : 0) * xmWorkflowService.filterMdOverlayBusinessKeysByStepStatusAndUser(["key2"], [WorkflowStatus.ACTIVE], user) >> Stream.of("key2")
    1 * xmWorkflowService.getDashboardIds(["key2"]) >> ["dashboardId"]
    1 * exceptionManagementEvidenceRepository.saveFile(["dashboardId"], evidence) >> right(new ExceptionManagementEvidence())
    (status == CalculationTestStatus.IN_PRELIMINARY ? 1 : 0) * xmWorkflowService.submitMdPreliminaryResolutionForm(["key2"], _)
    (status == CalculationTestStatus.IN_OVERLAY ? 1 : 0) * xmWorkflowService.submitMdOverlayResolutionForm(["key2"], _)

    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key2"]
    }

    where:
    status                               | _
    CalculationTestStatus.IN_PRELIMINARY | _
    CalculationTestStatus.IN_OVERLAY     | _
  }

  def "should query repository for progress"() {
    when:
    def result = service.progress("key")

    then:
    1 * xmStepInstanceQueryRepository.getMdEntryProgress("key") >> new CallActivityProgressView(totalCount: 30)

    and:
    result.totalCount == 30
  }

  def "should undo resolution"() {
    setup:
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)

    1 * userRepository.userEither(_ as Authentication) >> right(user)
    1 * xmWorkflowService.filterMdPreliminaryBusinessKeysByStepStatusAndUser(["resultId"], [WorkflowStatus.FINALIZING, WorkflowStatus.HELD], user) >> Stream.of()
    1 * taskExecutionRepository.executions(_) >> [new TaskExecution(id: "taskId", status: TaskExecutionStatus.IN_RESOLUTION, dashboardId: "id")]
    1 * repository.undoResolution(["resultId"], ["taskId"], CalculationTestStatus.IN_PRELIMINARY) >> right(new EntityId("1"))

    when:
    def result = service.undoResolution(auth, ["taskId"], ["resultId"], TableFilter.emptyTableFilter(), CalculationTestStatus.IN_PRELIMINARY)

    then:
    result == right(new EntityId("1"))
  }

  def "should undo preliminary workflow resolution"() {
    setup:
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)

    1 * userRepository.userEither(_ as Authentication) >> right(user)
    1 * xmWorkflowService.filterMdPreliminaryBusinessKeysByStepStatusAndUser(["resultId"], [WorkflowStatus.FINALIZING, WorkflowStatus.HELD], user) >> Stream.of("key1")
    1 * xmWorkflowService.clearMdPreliminaryResolution(["key1"])

    when:
    def result = service.undoResolution(auth, ["taskId"], ["resultId"], TableFilter.emptyTableFilter(), CalculationTestStatus.IN_PRELIMINARY)

    then:
    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key1"]
    }
  }

  def "should undo preliminary workflow approval"() {
    setup:
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)

    1 * userRepository.userEither(_ as Authentication) >> right(user)
    1 * xmWorkflowService.filterMdPreliminaryBusinessKeysByStepStatusAndUser(["resultId"], [WorkflowStatus.FINALIZING, WorkflowStatus.HELD], user) >> Stream.of("key1")
    1 * xmWorkflowService.clearMdPreliminaryApproval(["key1"])

    when:
    def result = service.undoApproval(auth, ["taskId"], ["resultId"], TableFilter.emptyTableFilter(), CalculationTestStatus.IN_PRELIMINARY)

    then:
    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key1"]
    }

    when:
    def resultWithoutTaskIds = service.undoApproval(auth, [], ["resultId"], TableFilter.emptyTableFilter(), CalculationTestStatus.IN_PRELIMINARY)

    then:
    resultWithoutTaskIds.isLeft()
    def error = resultWithoutTaskIds.left().getOrNull() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "No entries found to which this action could be applied."
  }

  def "should undo overlay workflow resolution"() {
    setup:
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)

    1 * userRepository.userEither(_ as Authentication) >> right(user)
    1 * xmWorkflowService.filterMdOverlayBusinessKeysByStepStatusAndUser(["resultId"], [WorkflowStatus.FINALIZING, WorkflowStatus.HELD], user) >> Stream.of("key1")
    1 * xmWorkflowService.clearMdOverlayResolution(["key1"])

    when:
    def result = service.undoResolution(auth, ["taskId"], ["resultId"], TableFilter.emptyTableFilter(), CalculationTestStatus.IN_OVERLAY)

    then:
    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key1"]
    }
  }

  def "should undo overlay workflow approval"() {
    setup:
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)

    1 * userRepository.userEither(_ as Authentication) >> right(user)
    1 * xmWorkflowService.filterMdOverlayBusinessKeysByStepStatusAndUser(["resultId"], [WorkflowStatus.FINALIZING, WorkflowStatus.HELD], user) >> Stream.of("key1")
    1 * xmWorkflowService.clearMdOverlayApproval(["key1"])

    when:
    def result = service.undoApproval(auth, ["taskId"], ["resultId"], TableFilter.emptyTableFilter(), CalculationTestStatus.IN_OVERLAY)

    then:
    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key1"]
    }

    when:
    def resultWithoutTaskIds = service.undoApproval(auth, [], ["resultId"], TableFilter.emptyTableFilter(), CalculationTestStatus.IN_PRELIMINARY)

    then:
    resultWithoutTaskIds.isLeft()
    def error = resultWithoutTaskIds.left().getOrNull() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "No entries found to which this action could be applied."
  }

  def "should verify resolution"() {
    setup:
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)

    1 * userRepository.userEither(_ as Authentication) >> right(user)
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(_, _, user, _) >> []
    1 * taskExecutionRepository.executions(_) >> [new TaskExecution(status: TaskExecutionStatus.IN_APPROVAL, dashboardId: "id")]
    0 * exceptionManagementEvidenceRepository.saveFile(["id"], null) >> right(new ExceptionManagementEvidence())
    1 * repository.verifyItems(_, _, new ExceptionManagementEvidence()) >> right(new EntityId("1"))

    when:
    def result = service.verifyResolutions(auth,
      new ApplyVerificationForm(null, null, ["id"]), null,
      CalculationTestStatus.IN_PRELIMINARY, null)

    then:
    result == right(new EntityId("1"))
  }

  def "should verify overall workflow resolution"() {
    setup:
    def evidence = Mock(MultipartFile)
    def user = user("user")
    def auth = new TestingAuthenticationToken(user, null)

    1 * userRepository.userEither(_ as Authentication) >> right(user)
    1 * taskDefinitionRepository.getTasksDefinitions(_) >> [(TaskExceptionManagementType.PRELIMINARY): new TasksDefinition()]
    1 * xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(["id"], [WorkflowStatus.ACTIVE], user, _) >> [new StepInstance(businessKey: "key1")]
    1 * xmWorkflowService.getDashboardIds(["key1"]) >> ["dashboardId"]
    1 * exceptionManagementEvidenceRepository.saveFile(["dashboardId"], evidence) >> right(new ExceptionManagementEvidence())
    1 * xmWorkflowService.submitMdPreliminaryApprovalForm(["key1"], _)

    when:
    def result = service.verifyResolutions(auth,
      new ApplyVerificationForm(null, new VerificationForm(VerificationStatus.VERIFIED, null), ["id"]), evidence,
      CalculationTestStatus.IN_PRELIMINARY, null)

    then:
    result.isRight()
    with(result.right().get()) { List<EntityId> it ->
      assert it*.id == ["key1"]
    }
  }

  def "should delegate getTasksFilterForPreliminary"() {
    setup:
    def form = new InstrumentFilterForm()
    def view = new ExceptionManagementCountedFiltersView()
    1 * repository.getPreliminaryTasksFilter(["taskId"], form) >> view

    expect:
    service.getTasksFilterForPreliminary(["taskId"], form) == view
  }

  def "should delegate getTasksFilterForOverlay"() {
    setup:
    def form = new InstrumentFilterForm()
    def view = new ExceptionManagementCountedFiltersView()
    1 * repository.getOverlayTasksFilter(["taskId"], form) >> view

    expect:
    service.getTasksFilterForOverlay(["taskId"], form) == view
  }

  def "should delegate preliminaryBreaksCount"() {
    setup:
    def form = new InstrumentResultBreaksFilterForm()
    def taskIds =  ["a", "b"]
    def view = new BreakCountView()
    1 * repository.preliminaryBreakCount(form, taskIds) >> view

    expect:
    service.preliminaryBreaksCount(form, taskIds) == view
  }

  def "should delegate overlayBreaksCount"() {
    setup:
    def form = new InstrumentResultBreaksFilterForm()
    def taskIds =  ["a", "b"]
    def view = new BreakCountView()
    1 * repository.overlayBreaksCount(form, taskIds) >> view

    expect:
    service.overlayBreaksCount(form, taskIds) == view
  }
}
