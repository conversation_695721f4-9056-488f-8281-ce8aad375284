package com.solum.xplain.xm.dashboards

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.COMPANY_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE_ANOTHER
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.EXISTING_PORTFOLIO_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PORTFOLIO_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.STATE_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.VALUATION_DATA_GROUP
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.GroupRequest
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.value.EntityNameView
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.market.repository.MarketDataGroupRepository
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.trs.market.TrsMarketDataGroupRepository
import com.solum.xplain.xm.dashboards.enums.DashboardType
import com.solum.xplain.xm.dashboards.forms.DashboardForm
import com.solum.xplain.xm.dashboards.forms.PricingSlotPortfolioForm
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboardsteps.DashboardStepsExecutionService
import com.solum.xplain.xm.excmngmt.process.view.ExceptionManagementCountedFiltersView
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.util.LinkedMultiValueMap
import spock.lang.Specification
import spock.lang.Unroll

@WebMvcTest(controllers = [DashboardController])
@MockMvcConfiguration
class DashboardControllerTest extends Specification {

  @SpringBean
  DashboardService service = Mock()

  @SpringBean
  DashboardStepsExecutionService dashboardStepsExecutionService = Mock()

  @SpringBean
  DashboardDateOverviewService datesOverviewService = Mock()

  @SpringBean
  DashboardInstrumentResultService instrumentResultService = Mock()

  @SpringBean
  DashboardPortfolioResultService portfolioResultService = Mock()

  @SpringBean
  DashboardRepository repository = Mock()

  @SpringBean
  MarketDataGroupRepository marketDataGroupRepository = Mock()

  @SpringBean
  TrsMarketDataGroupRepository trsMarketDataGroupRepository = Mock()

  @SpringBean
  CompanyRepository companyRepository = Mock()

  @Autowired
  MockMvc mockMvc

  @Autowired
  ObjectMapper mapper

  @WithMockUser
  def "should start dashboard"() {
    setup:
    dashboardStepsExecutionService.start("dashboardId", false) >> right(EntityId.entityId("id"))
    def results = mockMvc.perform(post("/dashboard/dashboardId/start")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("id") >= 0
    }
  }

  @WithMockUser
  def "should start dashboard with workflow"() {
    setup:
    dashboardStepsExecutionService.start("dashboardId", true) >> right(EntityId.entityId("id"))
    def results = mockMvc.perform(post("/dashboard/dashboardId/start")
      .queryParam("useWorkflow", "true")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("id") >= 0
    }
  }

  @WithMockUser
  @Unroll
  def "should perform form (#form) validation with response #code #responseBody inserting"() {
    setup:
    service.createDashboard(_ as BitemporalDate, _ as DashboardForm) >> right(EntityId.entityId("1"))

    marketDataGroupRepository.dataGroupName("id") >> Optional.of(new EntityNameView())
    marketDataGroupRepository.dataGroupName("id1") >> Optional.of(new EntityNameView())
    marketDataGroupRepository.dataGroupName("id2") >> Optional.empty()
    trsMarketDataGroupRepository.dataGroupName("id2") >> Optional.of(new EntityNameView())

    repository.existsByMarketDataGroup(DASHBOARD_DATE, DASHBOARD_DATE, "id", null) >> false
    repository.existsByMarketDataGroup(DASHBOARD_DATE, DASHBOARD_DATE, "id1", null) >> true

    repository.existsByPortfolios(DASHBOARD_DATE, DASHBOARD_DATE, [new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.OTHER)]) >> false
    repository.existsByPortfolios(DASHBOARD_DATE, DASHBOARD_DATE, [new PricingSlotPortfolioForm(EXISTING_PORTFOLIO_ID, PricingSlot.OTHER)]) >> true

    companyRepository.companyEntity(COMPANY_ID) >> right(new Company())
    def results = mockMvc.perform(post("/dashboard")
      .with(csrf())
      .content(mapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                                                | code | responseBody
    // MARKET_DATA BATCH
    formMarketDataBatch({ c -> c })                                                                                     | 200  | "id"
    formMarketDataBatch({ c -> c.remove("type") })                                                                      | 412  | "NotNull.dashboardForm.type"
    formMarketDataBatch({ c -> c.remove("dateRange") })                                                                 | 412  | "NotNull.dashboardForm.dateRange"
    formMarketDataBatch({ c -> c.dateRange.remove("startDate") })                                                       | 412  | "NotNull.dashboardForm.dateRange.startDate"
    formMarketDataBatch({ c -> c.dateRange.remove("endDate") })                                                         | 412  | "NotNull.dashboardForm.dateRange.endDate"
    formMarketDataBatch({ c -> c.dateRange.endDate = DASHBOARD_DATE_ANOTHER })                                          | 200  | "id"
    formMarketDataBatch({ c -> c.dateRange.endDate = DASHBOARD_DATE.minusDays(1) })                                     | 412  | "Date range end date must be equal or after start date"
    formMarketDataBatch({ c -> c.remove("stateDate") })                                                                 | 412  | "NotNull.dashboardForm.stateDate"
    formMarketDataBatch({ c -> c.marketDataGroupId = "id2" })                                                           | 412  | "Market Data group is not valid"
    formMarketDataBatch({ c -> c.portfolios = [new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.OTHER)] })        | 412  | "Null.dashboardForm.portfolios"
    formMarketDataBatch({ c -> c.marketDataGroupId = "id1" })                                                           | 412  | "Dashboard for this date and market data group already exists"
    // MARKET_DATA
    formMarketData({ c -> c })                                                                                          | 200  | "id"
    formValuationData({ c -> c.remove("type") })                                                                        | 412  | "NotNull.dashboardForm.type"
    formMarketData({ c -> c.remove("dateRange") })                                                                      | 412  | "NotNull.dashboardForm.dateRange"
    formMarketData({ c -> c.dateRange.remove("startDate") })                                                            | 412  | "NotNull.dashboardForm.dateRange.startDate"
    formMarketData({ c -> c.dateRange.remove("endDate") })                                                              | 412  | "NotNull.dashboardForm.dateRange.endDate"
    formMarketData({ c -> c.dateRange.endDate = DASHBOARD_DATE_ANOTHER })                                               | 412  | "Date Range start and end dates must match"
    formMarketData({ c -> c.dateRange.endDate = DASHBOARD_DATE.minusDays(1) })                                          | 412  | "Date Range start and end dates must match"
    formMarketData({ c -> c.remove("stateDate") })                                                                      | 412  | "NotNull.dashboardForm.stateDate"
    formMarketData({ c -> c.marketDataGroupId = "id2" })                                                                | 412  | "Market Data group is not valid"
    formMarketData({ c -> c.portfolios = [new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.OTHER)] })             | 412  | "Null.dashboardForm.portfolios"
    formMarketData({ c -> c.marketDataGroupId = "id1" })                                                                | 412  | "Dashboard for this date and market data group already exists"
    // VALUATION_DATA
    formValuationData({ c -> c })                                                                                       | 200  | "id"
    formValuationData({ c -> c.portfolios = [new PricingSlotPortfolioForm(EXISTING_PORTFOLIO_ID, PricingSlot.OTHER)] }) | 200  | "id"
    formValuationData({ c -> c.remove("type") })                                                                        | 412  | "NotNull.dashboardForm.type"
    formValuationData({ c -> c.remove("dateRange") })                                                                   | 412  | "NotNull.dashboardForm.dateRange"
    formValuationData({ c -> c.dateRange.remove("startDate") })                                                         | 412  | "NotNull.dashboardForm.dateRange.startDate"
    formValuationData({ c -> c.dateRange.remove("endDate") })                                                           | 412  | "NotNull.dashboardForm.dateRange.endDate"
    formValuationData({ c -> c.dateRange.endDate = DASHBOARD_DATE_ANOTHER })                                            | 412  | "Date Range start and end dates must match"
    formValuationData({ c -> c.dateRange.endDate = DASHBOARD_DATE.minusDays(1) })                                       | 412  | "Date Range start and end dates must match"
    formValuationData({ c -> c.remove("stateDate") })                                                                   | 412  | "NotNull.dashboardForm.stateDate"
    formValuationData({ c -> c.marketDataGroupId = "id1" })                                                             | 412  | "Null.marketDataGroupId"
    formValuationData({ c -> c.remove("portfolios") })                                                                  | 412  | "NotNull.dashboardForm.portfolios"
  }

  @WithMockUser
  def "should get all dashboards"() {
    setup:
    1 * service.getDashboards(TableFilter.emptyTableFilter(),
      ScrollRequest.of(0, 10), STATE_DATE, false) >> ScrollableEntry.empty()

    when:
    def results = mockMvc.perform(get("/dashboard")
      .param("stateDate", STATE_DATE.toString())
      .param("showLastWeekOnly", "false")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get result items"() {
    setup:
    1 * instrumentResultService.getResults("dashboardId",
      _,
      ScrollRequest.of(0, 10),
      GroupRequest.emptyGroupRequest()) >> right(ScrollableEntry.empty())

    when:
    def results = mockMvc
      .perform(get("/dashboard/dashboardId/result-items")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .param("stateDate", DASHBOARD_DATE.toString())
      .param("onlyCurvesWithBreaks", "false")
      .param("includeHeld", "false")
      .param("onboardingOnly", "false")
      .param("filter.providers", "[[]]")
      .param("filter.curves", "[[]]")
      .param("filter.bidAskTypes", "")
      .param("filter.curveConfigurations", "[[]]")
      .param("filter.assetFilter.assetClasses", "FX_RATES")
      .param("filter.assetFilter.fxPairs", "[[]]")).andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get result items csv"() {
    setup:
    1 * instrumentResultService.getResultsCsv("dashboardId", _, _) >> right(FileResponseEntity.csvFile(new ByteArrayResource("csv".bytes), "MDExceptionMngtResults.csv"))

    when:
    def results = mockMvc
      .perform(get("/dashboard/dashboardId/result-items/csv")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .param("stateDate", DASHBOARD_DATE.toString())
      .param("onlyCurvesWithBreaks", "false")
      .param("includeHeld", "false")
      .param("onboardingOnly", "false")
      .param("filter.providers", "[[]]")
      .param("filter.curves", "[[]]")
      .param("filter.bidAskTypes", "")
      .param("filter.curveConfigurations", "[[]]")
      .param("filter.assetFilter.assetClasses", "FX_RATES")
      .param("filter.assetFilter.fxPairs", "[[]]")).andReturn()

    then:
    results.response.status == 200
    results.response.getHeader("Content-Disposition").contains("filename=\"MDExceptionMngtResults.csv\"")
  }

  def "should get PV calculation result items csv"() {
    setup:
    service.getAllDashboardPVCalculationResults("dashboardId",
      LocalDate.now(),
      _ as TableFilter) >> result
    when:
    def results = mockMvc.perform(get('/dashboard/dashboardId/portfolios/calculation-results/csv')
      .param("stateDate", LocalDate.now().toString())
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf()))
      .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    result                                                                                  | code | responseBody
    right(FileResponseEntity.csvFile(new ByteArrayResource("a".getBytes("UTF-8")), "name")) | 200  | ""
    left([Error.OBJECT_NOT_FOUND.entity()])                                                 | 422  | "OBJECT_NOT_FOUND"
  }

  @WithMockUser
  def "should get result items breaks counts"() {
    setup:
    1 * instrumentResultService.resultsItemsBreaksCount("dashboardId", _) >> right(List.of())

    when:
    def results = mockMvc
      .perform(get("/dashboard/dashboardId/result-items/break-counts")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .param("onlyCurvesWithBreaks", "false")
      .param("includeHeld", "false")
      .param("onboardingOnly", "false")
      .param("filter.providers", "[[]]")
      .param("filter.curves", "[[]]")
      .param("filter.bidAskTypes", "")
      .param("filter.curveConfigurations", "[[]]")
      .param("filter.assetFilter.assetClasses", "FX_RATES")
      .param("filter.assetFilter.fxPairs", "[[]]")).andReturn()

    then:
    results.response.status == 200
  }

  @WithMockUser
  def "should get result items filters"() {
    setup:
    1 * instrumentResultService.resultItemsCountedFilter("dashboardId", _) >> right(new ExceptionManagementCountedFiltersView())

    when:
    def results = mockMvc
      .perform(get("/dashboard/dashboardId/result-items/filters")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .param("providers", "[[]]")
      .param("curves", "[[]]")
      .param("bidAskTypes", "")
      .param("curveConfigurations", "[[]]")
      .param("assetFilter.assetClasses", "FX_RATES")
      .param("assetFilter.fxPairs", "[[]]")).andReturn()

    then:
    results.response.status == 200
  }

  static def formMarketDataBatch(Closure c) {
    return [type             : DashboardType.MARKET_DATA_BATCH,
      dateRange        : [startDate: DASHBOARD_DATE, endDate: DASHBOARD_DATE],
      stateDate        : DASHBOARD_DATE.plusDays(1),
      marketDataGroupId: "id",
      portfolios       : null].with(true, c)
  }

  static def formMarketData(Closure c) {
    return [type             : DashboardType.MARKET_DATA,
      dateRange        : [startDate: DASHBOARD_DATE, endDate: DASHBOARD_DATE],
      stateDate        : DASHBOARD_DATE.plusDays(1),
      marketDataGroupId: "id",
      portfolios       : null].with(true, c)
  }

  static def formValuationData(Closure c) {
    return [type             : DashboardType.VALUATION_DATA,
      dateRange        : [startDate: DASHBOARD_DATE, endDate: DASHBOARD_DATE],
      stateDate        : DASHBOARD_DATE.plusDays(1),
      marketDataGroupId: null,
      portfolios       : [
        [
          "portfolioId" : PORTFOLIO_ID,
          "ipvDataGroup": VALUATION_DATA_GROUP.entityId
        ]
      ]
    ].with(true, c)
  }
}
