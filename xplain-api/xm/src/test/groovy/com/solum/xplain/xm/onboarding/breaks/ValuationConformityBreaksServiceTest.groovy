package com.solum.xplain.xm.onboarding.breaks

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND
import static com.solum.xplain.core.portfolio.CoreProductType.FXOPT
import static com.solum.xplain.core.providers.DataProvider.XPLAIN_PROVIDER_CODE
import static com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType.PRIMARY
import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.AC_P1
import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.DC_XPLAIN
import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.MARKET_CONF
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.REQUESTED
import static com.solum.xplain.xm.onboarding.entity.OnboardingReportStatus.IN_PROGRESS
import static com.solum.xplain.xm.onboarding.entity.OnboardingReportStatus.READY_FOR_SUBMISSION
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.calculationapi.CalculationResultProvider
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.portfolio.value.OnboardingValuationMetrics
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.onboarding.OnboardingReportRepository
import com.solum.xplain.xm.onboarding.entity.OnboardingReport
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItem
import com.solum.xplain.xm.onboarding.entity.OnboardingTradeResultBreak
import com.solum.xplain.xm.onboarding.entity.OnboardingVendorMetrics
import org.bson.types.ObjectId
import spock.lang.Specification

class ValuationConformityBreaksServiceTest extends Specification {

  private static final BitemporalDate STATE_DATE = BitemporalDate.newOfNow()

  private static final def REPORT_ID = ObjectId.get()
  private static final def REPORT_ITEM_ID = ObjectId.get()
  private static final def CALCULATION_ID = ObjectId.get()
  private static final def PORTFOLIO_ID = "portfolioId"
  private static final def NAV_TRADE_DATE = BigDecimal.ZERO
  private static final def NAV_ONBOARDING_DATE = BigDecimal.ONE

  def onboardingReportRepository = Mock(OnboardingReportRepository)
  def calculationResultProvider = Mock(CalculationResultProvider)
  def breakTestCalculationsProvider = Mock(OnboardingBreakTestCalculationsProvider)

  def onboardingBreakTestCalculationsDcXplain = Mock(OnboardingBreakTestCalculations)
  def onboardingBreakTestCalculationsMarketConf = Mock(OnboardingBreakTestCalculations)
  def onboardingBreakTestCalculationsVendorConf = Mock(OnboardingBreakTestCalculations)

  def service = new ValuationConformityBreaksService(
  onboardingReportRepository,
  calculationResultProvider,
  breakTestCalculationsProvider
  )

  def "should correctly do nothing when no applicable results"() {
    setup:
    def invalidReportId = ObjectId.get()
    1 * onboardingReportRepository.findReportById(REPORT_ID) >> right(new OnboardingReport(id: REPORT_ID, status: READY_FOR_SUBMISSION))
    1 * onboardingReportRepository.findReportById(invalidReportId) >> left(OBJECT_NOT_FOUND.entity("Not found"))

    when:
    service.processCalculationBreaks(REPORT_ID, CALCULATION_ID, PORTFOLIO_ID)
    service.processCalculationBreaks(invalidReportId, CALCULATION_ID, PORTFOLIO_ID)

    then:
    0 * calculationResultProvider.calculationResultsOnboarding(CALCULATION_ID)
  }

  def "should correctly process single item"() {
    setup:
    def trade = new Trade(
      externalTradeId: "TRADE_ID",
      productType: FXOPT,
      )
    def reportItem = new OnboardingReportItem(
      id: REPORT_ITEM_ID,
      calculationId: CALCULATION_ID,
      xplainCheckStatus: REQUESTED,
      marketCheckStatus: REQUESTED,
      vendorCheckStatus: REQUESTED,
      vendorPrimaryMetrics: new OnboardingVendorMetrics(provider: XPLAIN_PROVIDER_CODE),
      navOnTradeDate: NAV_TRADE_DATE,
      navOnVendorOnboardingDate: NAV_ONBOARDING_DATE,
      trade: trade)

    1 * onboardingReportRepository.findReportById(REPORT_ID) >> right(new OnboardingReport(
      id: REPORT_ID,
      status: IN_PROGRESS,
      stateDate: STATE_DATE.getActualDate(),
      recordDate: STATE_DATE.getRecordDate(),
      xplainConformity: true,
      marketConformity: true,
      vendorConformity: true,
      ))
    def metrics = new OnboardingValuationMetrics(externalTradeId: trade.getExternalTradeId(), productType: FXOPT)
    1 * calculationResultProvider.calculationResultsOnboarding(CALCULATION_ID) >> [metrics]
    1 * onboardingReportRepository.itemsByReportAndPortfolio(REPORT_ID, PORTFOLIO_ID) >> [reportItem]
    1 * breakTestCalculationsProvider.provideCalculations(STATE_DATE, DC_XPLAIN) >> onboardingBreakTestCalculationsDcXplain
    1 * breakTestCalculationsProvider.provideCalculations(STATE_DATE, MARKET_CONF) >> onboardingBreakTestCalculationsMarketConf
    1 * breakTestCalculationsProvider.provideCalculations(STATE_DATE, AC_P1) >> onboardingBreakTestCalculationsVendorConf

    def breakResultDcXplain = Mock(OnboardingTradeResultBreak)
    1 * onboardingBreakTestCalculationsDcXplain.process(trade, new OnboardingBreakTestData(nav: NAV_TRADE_DATE)) >> [breakResultDcXplain]

    def breakResultMarketConf = Mock(OnboardingTradeResultBreak)
    1 * onboardingBreakTestCalculationsMarketConf.process(trade, new OnboardingBreakTestData(nav: NAV_TRADE_DATE)) >> [breakResultMarketConf]

    0 * onboardingBreakTestCalculationsVendorConf.process(_, _, _)

    when:
    service.processCalculationBreaks(REPORT_ID, CALCULATION_ID, PORTFOLIO_ID)

    then:
    1 * onboardingReportRepository.updateValuationMetrics(Map.of(reportItem, metrics), CALCULATION_ID)
    1 * onboardingReportRepository.updateBreakResults(Map.of(REPORT_ITEM_ID, [breakResultDcXplain]), DC_XPLAIN)
    1 * onboardingReportRepository.updateBreakResults(Map.of(REPORT_ITEM_ID, [breakResultMarketConf]), MARKET_CONF)
    1 * onboardingReportRepository.updateBreakResults([:], AC_P1)
  }

  def "should correctly process single item with vendor calculation only"() {
    setup:
    def trade = new Trade(
      externalTradeId: "TRADE_ID",
      productType: FXOPT,
      )
    def reportItem = new OnboardingReportItem(
      id: REPORT_ITEM_ID,
      vendorCalculationId: CALCULATION_ID,
      xplainCheckStatus: REQUESTED,
      marketCheckStatus: REQUESTED,
      vendorCheckStatus: REQUESTED,
      vendorPrimaryMetrics: new OnboardingVendorMetrics(provider: XPLAIN_PROVIDER_CODE),
      navOnTradeDate: NAV_TRADE_DATE,
      navOnVendorOnboardingDate: NAV_ONBOARDING_DATE,
      trade: trade)

    1 * onboardingReportRepository.findReportById(REPORT_ID) >> right(new OnboardingReport(
      id: REPORT_ID,
      status: IN_PROGRESS,
      stateDate: STATE_DATE.getActualDate(),
      recordDate: STATE_DATE.getRecordDate(),
      xplainConformity: true,
      marketConformity: true,
      vendorConformity: true,
      ))
    def metrics = new OnboardingValuationMetrics(externalTradeId: trade.getExternalTradeId(), productType: FXOPT)
    1 * calculationResultProvider.calculationResultsOnboarding(CALCULATION_ID) >> [metrics]
    1 * onboardingReportRepository.itemsByReportAndPortfolio(REPORT_ID, PORTFOLIO_ID) >> [reportItem]
    1 * breakTestCalculationsProvider.provideCalculations(STATE_DATE, DC_XPLAIN) >> onboardingBreakTestCalculationsDcXplain
    1 * breakTestCalculationsProvider.provideCalculations(STATE_DATE, MARKET_CONF) >> onboardingBreakTestCalculationsMarketConf
    1 * breakTestCalculationsProvider.provideCalculations(STATE_DATE, AC_P1) >> onboardingBreakTestCalculationsVendorConf

    0 * onboardingBreakTestCalculationsDcXplain.process(_, _, _)
    0 * onboardingBreakTestCalculationsMarketConf.process(_, _, _)

    def breakResultVendorConf = Mock(OnboardingTradeResultBreak)
    1 * onboardingBreakTestCalculationsVendorConf.process(trade, new OnboardingBreakTestData(nav: NAV_ONBOARDING_DATE), PRIMARY) >> [breakResultVendorConf]

    when:
    service.processCalculationBreaks(REPORT_ID, CALCULATION_ID, PORTFOLIO_ID)

    then:
    1 * onboardingReportRepository.updateValuationMetrics(Map.of(reportItem, metrics), CALCULATION_ID)
    1 * onboardingReportRepository.updateBreakResults([:], DC_XPLAIN)
    1 * onboardingReportRepository.updateBreakResults([:], MARKET_CONF)
    1 * onboardingReportRepository.updateBreakResults(Map.of(REPORT_ITEM_ID, [breakResultVendorConf]), AC_P1)
  }
}
