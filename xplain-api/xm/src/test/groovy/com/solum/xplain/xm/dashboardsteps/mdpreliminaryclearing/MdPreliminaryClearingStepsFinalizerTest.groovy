package com.solum.xplain.xm.dashboardsteps.mdpreliminaryclearing


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.UNIQUE_PRELIMINARY_TASK
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMarketDataUpload
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdPreliminaryClearing
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdPreliminaryRun

import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository
import io.atlassian.fugue.Either
import spock.lang.Specification

class MdPreliminaryClearingStepsFinalizerTest extends Specification {

  DashboardStepProcessor processor = Mock()
  TaskExecutionRepository taskExecutionRepository = Mock()

  MdPreliminaryClearingStepsFinalizer finalizer = new MdPreliminaryClearingStepsFinalizer(processor, taskExecutionRepository)

  def "should update preliminary clearing step"() {
    1 * processor.getMdSteps(MARKET_DATA_DASHBOARD) >> entries()

    1 * taskExecutionRepository.allTasksCompleted(UNIQUE_PRELIMINARY_TASK) >> true

    1 * processor.updateMdSteps(_) >> { List<?> args ->
      def steps = args[0] as List<DashboardEntryMd>
      assert steps.size() == 1
      assert steps[0].step == DashboardStep.MD_PRELIMINARY_CLEARING
      assert steps[0].status == StepStatus.COMPLETED
      return Either.right(steps)
    }

    when:
    def result = finalizer.execute(MARKET_DATA_DASHBOARD)

    then:
    result.isRight()
  }

  def "should not update preliminary clearing step when not all tasks finished"() {
    1 * processor.getMdSteps(MARKET_DATA_DASHBOARD) >> entries()

    1 * taskExecutionRepository.allTasksCompleted(UNIQUE_PRELIMINARY_TASK) >> false

    1 * processor.updateMdSteps([]) >> Either.right([])

    when:
    def result = finalizer.execute(MARKET_DATA_DASHBOARD)

    then:
    result.isRight()
  }

  static List<DashboardEntryMd> entries() {
    return [
      dashboardStepMarketDataUpload(),
      dashboardStepMdPreliminaryRun(),
      dashboardStepMdPreliminaryClearing().tap {
        status = StepStatus.IN_PROGRESS
      }
    ]
  }
}
