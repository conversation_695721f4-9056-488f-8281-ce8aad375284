package com.solum.xplain.xm.onboarding.breaks


import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.AC_P1
import static com.solum.xplain.xm.excmngmt.rulesonboarding.value.OnboardingTestType.DC_XPLAIN

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.xm.excmngmt.rulesonboarding.OnboardingBreakTest
import com.solum.xplain.xm.excmngmt.rulesonboarding.OnboardingBreakTestRepository
import spock.lang.Specification

class OnboardingBreakTestCalculationsProviderTest extends Specification {

  def onboardingBreakTestRepository = Mock(OnboardingBreakTestRepository)

  def provider = new OnboardingBreakTestCalculationsProvider(onboardingBreakTestRepository)

  def "should correctly provide calculation"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def vendorTest = new OnboardingBreakTest(type: AC_P1)
    def xplainTest = new OnboardingBreakTest(type: DC_XPLAIN)
    1 * onboardingBreakTestRepository.latestTests(stateDate) >> [vendorTest, xplainTest]

    when:
    def result = provider.provideCalculations(stateDate, AC_P1)

    then:
    result == new OnboardingBreakTestCalculations([vendorTest])
  }
}
