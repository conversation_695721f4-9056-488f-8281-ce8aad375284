package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.dashboardStepMdBatchPreliminaryClearing
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.COMPLETED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.REQUESTED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.UPDATED

import com.solum.xplain.core.error.Error
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboardsteps.dashboardfinalize.FinalizeDashboardEvent
import io.atlassian.fugue.Either
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class MdBatchPreliminaryClearingEventListenerTest extends Specification {

  DashboardRepository dashboardRepository = Mock()
  ApplicationEventPublisher publisher = Mock()
  MdBatchPreliminaryClearingStepsInitializer clearingStepsInitializer = Mock()
  MdBatchPreliminaryClearingStepsFinalizer clearingStepsFinalizer = Mock()
  MdBatchPreliminaryClearingCleanup stepCleanup = Mock()

  MdBatchPreliminaryClearingEventListener listener = new MdBatchPreliminaryClearingEventListener(dashboardRepository,
  publisher,
  clearingStepsInitializer,
  clearingStepsFinalizer, stepCleanup)

  def setup() {
    dashboardRepository.dashboard(MD_BATCH_DASHBOARD_ID) >> Either.right(MD_BATCH_DASHBOARD)
  }

  def "should correctly process MD_BATCH_PRELIMINARY_CLEARING step event"() {
    when: "REQUESTED - initialize clearing steps with all steps completed"
    listener.onEvent(MdBatchPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * clearingStepsInitializer.execute(MD_BATCH_DASHBOARD) >> Either.right(dashboardStepMdBatchPreliminaryClearing())
    1 * publisher.publishEvent(MdBatchPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    when: "REQUESTED - initialize clearing steps with not all steps completed"
    listener.onEvent(MdBatchPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * clearingStepsInitializer.execute(MD_BATCH_DASHBOARD) >> Either.right(dashboardStepMdBatchPreliminaryClearing().tap { status = StepStatus.IN_PROGRESS })
    0 * publisher.publishEvent(_)

    when: "UPDATED - finalize clearing steps with all steps completed"
    listener.onEvent(MdBatchPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(UPDATED)
      .build())

    then:
    1 * clearingStepsFinalizer.execute(MD_BATCH_DASHBOARD) >> Either.right(dashboardStepMdBatchPreliminaryClearing())
    1 * publisher.publishEvent(MdBatchPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    when: "UPDATED - finalize clearing steps with not all steps completed"
    listener.onEvent(MdBatchPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(UPDATED)
      .build())

    then:
    1 * clearingStepsFinalizer.execute(MD_BATCH_DASHBOARD) >> Either.right(dashboardStepMdBatchPreliminaryClearing().tap { status = StepStatus.IN_PROGRESS })
    0 * publisher.publishEvent(_)


    when: "COMPLETED - do nothing"
    listener.onEvent(MdBatchPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_BATCH_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    then:
    1 * publisher.publishEvent(new FinalizeDashboardEvent(MD_BATCH_DASHBOARD_ID))
  }

  def "should correctly process MD_BATCH_PRELIMINARY_CLEARING step event when dashboard deleted"() {
    setup:
    def dashboardId = "someId"
    def dashboard = new Dashboard(id: "DELETED")
    1 * dashboardRepository.dashboard(dashboardId) >> Either.right(dashboard)
    1 * dashboardRepository.dashboard("DELETED") >> Either.left(Error.OBJECT_NOT_FOUND.entity())
    1 * clearingStepsInitializer.execute(dashboard) >> Either.right(dashboardStepMdBatchPreliminaryClearing().tap { status = StepStatus.IN_PROGRESS })

    when:
    listener.onEvent(MdBatchPreliminaryClearingEvent
      .builder()
      .dashboardId(dashboardId)
      .type(REQUESTED)
      .build())

    then:
    0 * publisher._
    1 * stepCleanup.execute("DELETED")
  }
}
