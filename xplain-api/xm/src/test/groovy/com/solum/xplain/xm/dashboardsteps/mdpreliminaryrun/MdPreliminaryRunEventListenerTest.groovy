package com.solum.xplain.xm.dashboardsteps.mdpreliminaryrun


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MARKET_DATA_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.COMPLETED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.REQUESTED
import static com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type.UPDATED

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.error.Error
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.dashboardsteps.mdpreliminaryclearing.MdPreliminaryClearingEvent
import io.atlassian.fugue.Either
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class MdPreliminaryRunEventListenerTest extends Specification {

  private static final RESULT_ID = "exceptionManagementResultId"

  DashboardRepository dashboardRepository = Mock()
  ApplicationEventPublisher publisher = Mock()
  MdPreliminaryRunExecutor runExecutor = Mock()
  MdPreliminaryRunCleanup stepCleanup = Mock()

  MdPreliminaryRunEventListener listener = new MdPreliminaryRunEventListener(
  dashboardRepository,
  publisher,
  runExecutor,
  stepCleanup
  )

  def setup() {
    dashboardRepository.dashboard(MD_DASHBOARD_ID) >> Either.right(MARKET_DATA_DASHBOARD)
  }

  def "should correctly process MD_PRELIMINARY_RUN step event when REQUESTED"() {
    when:
    listener.onEvent(MdPreliminaryRunEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())

    then:
    1 * runExecutor.execute(MARKET_DATA_DASHBOARD) >> Either.right(EntityId.entityId(RESULT_ID))
    1 * publisher.publishEvent(MdPreliminaryRunEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build()
      )
  }

  def "should correctly process MD_PRELIMINARY_RUN step event when UPDATED"() {
    when:
    listener.onEvent(MdPreliminaryRunEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(UPDATED)
      .build())

    then:
    0 * runExecutor._
    0 * publisher._
  }

  def "should correctly process MD_PRELIMINARY_RUN step event when COMPLETED"() {
    when:
    listener.onEvent(MdPreliminaryRunEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(COMPLETED)
      .build())

    then:
    1 * publisher.publishEvent(MdPreliminaryClearingEvent
      .builder()
      .dashboardId(MD_DASHBOARD_ID)
      .type(REQUESTED)
      .build())
  }

  def "should correctly process MD_PRELIMINARY_RUN step event when COMPLETED and dashboard deleted"() {
    setup:
    def anotherId = "anotherId"
    dashboardRepository.dashboard(anotherId) >> Either.left(Error.OBJECT_NOT_FOUND.entity())

    when:
    listener.onEvent(MdPreliminaryRunEvent
      .builder()
      .dashboardId(anotherId)
      .type(COMPLETED)
      .build())

    then:
    0 * publisher._
    1 * stepCleanup.execute(anotherId)
  }
}
