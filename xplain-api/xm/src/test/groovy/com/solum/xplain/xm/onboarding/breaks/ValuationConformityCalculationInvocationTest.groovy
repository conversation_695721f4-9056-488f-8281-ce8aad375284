package com.solum.xplain.xm.onboarding.breaks


import static com.solum.xplain.core.company.value.PortfolioSettings.settings
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.PRELIMINARY_PRIMARY
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.PRELIMINARY_SECONDARY
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_PRIMARY
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_SECONDARY
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.REQUESTED

import com.solum.xplain.core.calculationapi.OnboardingCalculation
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.CompanyPortfolioSettingsResolver
import com.solum.xplain.core.company.value.CompanyLegalEntityIpvSettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.value.PortfolioView
import com.solum.xplain.core.providers.DataProvider
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.xm.dashboardsteps.opvvaluations.DashboardMarketDataSourceResolver
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.onboarding.OnboardingReportRepository
import com.solum.xplain.xm.onboarding.entity.OnboardingReport
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItem
import com.solum.xplain.xm.onboarding.entity.OnboardingVendorMetrics
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.types.ObjectId
import spock.lang.Specification

class ValuationConformityCalculationInvocationTest extends Specification {

  def settingsResolver = Mock(CompanyPortfolioSettingsResolver)
  def onboardingCalculation = Mock(OnboardingCalculation)
  def onboardingReportRepository = Mock(OnboardingReportRepository)
  def marketDataSourceResolver = Mock(DashboardMarketDataSourceResolver)

  def service = new ValuationConformityCalculationInvocation(
  settingsResolver,
  onboardingCalculation,
  onboardingReportRepository,
  marketDataSourceResolver,
  )

  def "should correctly do nothing when no applicable results"() {
    setup:
    def report = new OnboardingReport()

    when:
    def results = service.invokeValuations([], report)

    then:
    results.isEmpty()
    0 * onboardingReportRepository.updateFailedValuationItems(_, _, _, _)
    0 * onboardingReportRepository.updateValuationItemsCalculationId(_, _, _, _, false, false)
    0 * settingsResolver.portfoliosSettings(_)
    0 * marketDataSourceResolver.resolve(_, _)
    0 * onboardingCalculation.calculate(_, _, _, _, _, _)
  }

  def "should correctly split by portfolio and trade date and invoke separate calculations"() {
    setup:
    def reportId = ObjectId.get()
    def user = Mock(AuditUser)
    def tradeDate1 = LocalDate.of(2023, 1, 1)
    def tradeDate2 = LocalDate.of(2023, 1, 2)
    def tradeDate3 = LocalDate.of(2023, 1, 3)
    def recordDate = LocalDateTime.now()

    def report = new OnboardingReport(id: reportId, recordDate: recordDate, createdBy: user)
    def itemPortfolio1Date1 = new OnboardingReportItem(
      xplainCheckStatus: REQUESTED,
      trade: new Trade(
      portfolioId: "P1",
      tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: tradeDate1))
      )
      )
    def itemPortfolio1Date2 = new OnboardingReportItem(
      xplainCheckStatus: REQUESTED,
      trade: new Trade(
      portfolioId: "P1",
      tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: tradeDate2))
      )
      )
    def itemPortfolio2 = new OnboardingReportItem(
      marketCheckStatus: REQUESTED,
      trade: new Trade(
      portfolioId: "P2",
      tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: tradeDate1))
      )
      )
    def itemPortfolio2VendorVersionDate1 = new OnboardingReportItem(
      vendorCheckStatus: REQUESTED,
      vendorPrimaryMetrics: new OnboardingVendorMetrics(provider: DataProvider.XPLAIN_PROVIDER_CODE),
      trade: new Trade(
      portfolioId: "P2",
      vendorOnboardingDate: tradeDate1,
      tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: LocalDate.MAX))
      )
      )
    def itemPortfolio2VendorVersionDate3 = new OnboardingReportItem(
      vendorCheckStatus: REQUESTED,
      vendorPrimaryMetrics: new OnboardingVendorMetrics(provider: DataProvider.XPLAIN_PROVIDER_CODE),
      trade: new Trade(
      portfolioId: "P2",
      vendorOnboardingDate: tradeDate3,
      tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: LocalDate.MAX))
      )
      )
    def itemPortfolio3VendorNoXplainProvider = new OnboardingReportItem(
      vendorCheckStatus: REQUESTED,
      trade: new Trade(
      portfolioId: "P2",
      vendorOnboardingDate: LocalDate.MAX,
      tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: LocalDate.MAX))
      )
      )
    def itemPortfolio4VendorWithoutOnboardingDate = new OnboardingReportItem(
      vendorCheckStatus: REQUESTED,
      vendorPrimaryMetrics: new OnboardingVendorMetrics(provider: DataProvider.XPLAIN_PROVIDER_CODE),
      trade: new Trade(
      portfolioId: "P2",
      tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: LocalDate.MAX))
      )
      )

    def vs11 = CompanyLegalEntitySettingsView.newOf(
      new CompanyLegalEntityValuationSettingsView(marketDataGroupId: "md11"),
      new CompanyLegalEntityIpvSettingsView()
      )
    def vs12 = CompanyLegalEntitySettingsView.newOf(
      new CompanyLegalEntityValuationSettingsView(marketDataGroupId: "md12"),
      new CompanyLegalEntityIpvSettingsView()
      )
    def vs21 = CompanyLegalEntitySettingsView.newOf(
      new CompanyLegalEntityValuationSettingsView(marketDataGroupId: "md21"),
      new CompanyLegalEntityIpvSettingsView()
      )
    def vs22 = CompanyLegalEntitySettingsView.newOf(
      new CompanyLegalEntityValuationSettingsView(marketDataGroupId: "md22"),
      new CompanyLegalEntityIpvSettingsView()
      )

    def portfolio1Date1Settings = settings(new PortfolioView(id: "P1"), vs11)
    def portfolio1Date2Settings = settings(new PortfolioView(id: "P1"), vs12)
    def portfolio2Date1Settings = settings(new PortfolioView(id: "P2"), vs21)
    def portfolio2Date2Settings = settings(new PortfolioView(id: "P2"), vs22)


    1 * settingsResolver.portfoliosSettings(BitemporalDate.newOf(tradeDate1, recordDate)) >> [portfolio1Date1Settings, portfolio2Date1Settings]
    1 * settingsResolver.portfoliosSettings(BitemporalDate.newOf(tradeDate2, recordDate)) >> [portfolio1Date2Settings, portfolio2Date2Settings]
    1 * settingsResolver.portfoliosSettings(BitemporalDate.newOf(tradeDate3, recordDate)) >> [portfolio2Date2Settings]

    1 * marketDataSourceResolver.resolve("md11", tradeDate1) >> RAW_PRIMARY
    1 * marketDataSourceResolver.resolve("md21", tradeDate1) >> RAW_SECONDARY
    1 * marketDataSourceResolver.resolve("md12", tradeDate2) >> PRELIMINARY_PRIMARY
    1 * marketDataSourceResolver.resolve("md22", tradeDate3) >> PRELIMINARY_SECONDARY
    0 * marketDataSourceResolver.resolve("md22", tradeDate2)

    def calculation1Id = ObjectId.get()
    def calculation2Id = ObjectId.get()
    def calculation3Id = ObjectId.get()
    def calculation4Id = ObjectId.get()
    1 * onboardingCalculation.calculate(reportId, tradeDate1, user, portfolio1Date1Settings, RAW_PRIMARY, BitemporalDate.newOf(tradeDate1, recordDate)) >> Either.right(EntityId.entityId(calculation1Id))
    1 * onboardingCalculation.calculate(reportId, tradeDate1, user, portfolio2Date1Settings, RAW_SECONDARY, BitemporalDate.newOf(tradeDate1, recordDate)) >> Either.right(EntityId.entityId(calculation2Id))
    1 * onboardingCalculation.calculate(reportId, tradeDate2, user, portfolio1Date2Settings, PRELIMINARY_PRIMARY, BitemporalDate.newOf(tradeDate2, recordDate)) >> Either.right(EntityId.entityId(calculation3Id))
    1 * onboardingCalculation.calculate(reportId, tradeDate3, user, portfolio2Date2Settings, PRELIMINARY_SECONDARY, BitemporalDate.newOf(tradeDate3, recordDate)) >> Either.right(EntityId.entityId(calculation4Id))

    when:
    def results = service.invokeValuations(
      [
        itemPortfolio1Date1,
        itemPortfolio1Date2,
        itemPortfolio2,
        itemPortfolio2VendorVersionDate1,
        itemPortfolio2VendorVersionDate3,
        itemPortfolio3VendorNoXplainProvider,
        itemPortfolio4VendorWithoutOnboardingDate,
      ],
      report)

    then:
    results.isEmpty()
    1 * onboardingReportRepository.updateValuationItemsCalculationId(reportId, portfolio1Date1Settings.view.id, tradeDate1, calculation1Id, true, false)
    1 * onboardingReportRepository.updateValuationItemsCalculationId(reportId, portfolio2Date1Settings.view.id, tradeDate1, calculation2Id, true, true)
    1 * onboardingReportRepository.updateValuationItemsCalculationId(reportId, portfolio1Date2Settings.view.id, tradeDate2, calculation3Id, true, false)
    1 * onboardingReportRepository.updateValuationItemsCalculationId(reportId, portfolio2Date2Settings.view.id, tradeDate3, calculation4Id, false, true)
    0 * onboardingReportRepository.updateFailedValuationItems(_, _, _, _)
  }

  def "should correctly store error messages when failed"() {
    setup:
    def reportId = ObjectId.get()
    def user = Mock(AuditUser)
    def tradeDate1 = LocalDate.of(2023, 1, 1)
    def recordDate = LocalDateTime.now()

    def report = new OnboardingReport(id: reportId, recordDate: recordDate, createdBy: user)
    def itemPortfolio1Date1 = new OnboardingReportItem(
      xplainCheckStatus: REQUESTED,
      trade: new Trade(
      portfolioId: "P1",
      tradeDetails: new TradeDetails(info: new TradeInfoDetails(tradeDate: tradeDate1))
      )
      )
    def vs11 = CompanyLegalEntitySettingsView.newOf(
      new CompanyLegalEntityValuationSettingsView(marketDataGroupId: "md11"),
      new CompanyLegalEntityIpvSettingsView()
      )

    def portfolio1Date1Settings = settings(new PortfolioView(id: "P1"), vs11)
    1 * settingsResolver.portfoliosSettings(BitemporalDate.newOf(tradeDate1, recordDate)) >> [portfolio1Date1Settings]
    1 * marketDataSourceResolver.resolve("md11", tradeDate1) >> RAW_PRIMARY
    1 * onboardingCalculation.calculate(reportId, tradeDate1, user, portfolio1Date1Settings, RAW_PRIMARY, BitemporalDate.newOf(tradeDate1, recordDate)) >> Either.left(Error.UNEXPECTED_ERROR.entity("Failed item"))

    when:
    def results = service.invokeValuations([itemPortfolio1Date1], report)

    then:
    results == [Error.CALCULATION_ERROR.entity("Failed to invoke valuation on date 2023-01-01: Failed item")]
    1 * onboardingReportRepository.updateFailedValuationItems(reportId, "P1", tradeDate1, "Failed to invoke valuation on date 2023-01-01: Failed item", true, false)
    0 * onboardingReportRepository.updateValuationItemsCalculationId(_, _, _, _)
  }
}
