package com.solum.xplain.xm.dashboardsteps

import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.marketDataBatchDashboard
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.marketDataDashboard
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.valuationDataDashboard
import static com.solum.xplain.xm.dashboards.entity.DashboardEntryBuilder.marketDataDashboardSteps

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.workflow.XmWorkflowService
import io.atlassian.fugue.Either
import java.time.LocalDateTime
import org.springframework.context.ApplicationEventPublisher
import spock.lang.Specification

class DashboardStepsExecutionServiceTest extends Specification {

  DashboardRepository dashboardRepository = Mock()
  DashboardStepProcessor processor = Mock()
  ApplicationEventPublisher publisher = Mock()
  XmWorkflowService workflowService = Mock()

  DashboardStepsExecutionService service = new DashboardStepsExecutionService(dashboardRepository,
  processor, publisher, workflowService)

  def "should start dashboard"() {
    setup:
    dashboard.startedAt = null
    1 * dashboardRepository.dashboard(dashboard.id) >> Either.right(dashboard)
    processor.getMdSteps(dashboard) >> []

    when:
    def result = service.start(dashboard.id, false)

    then:
    result.isRight()

    1 * publisher.publishEvent(_) >> { List<?> args ->
      def event = args[0] as DashboardEvent
      assert event.dashboardId == dashboard.id
      assert event.step == expectedStep
      assert event.type == DashboardEvent.Type.REQUESTED
    }
    1 * dashboardRepository.markInProgress(dashboard.id)

    where:
    dashboard                  | expectedStep
    marketDataDashboard()      | DashboardStep.MARKET_DATA_UPLOAD
    marketDataBatchDashboard() | DashboardStep.MARKET_DATA_BATCH_UPLOAD
  }

  def "should start valuation dashboard with workflow"() {
    setup:
    def dashboard = valuationDataDashboard()
    dashboard.startedAt = null
    1 * dashboardRepository.dashboard(dashboard.id) >> Either.right(dashboard)
    workflowService.valuationDataDashboardExists(dashboard.id) >> false

    when:
    def result = service.start(dashboard.id, true)

    then:
    result.isRight()
    1 * workflowService.startValuationDataDashboard(dashboard.id, dashboard.vdExceptionManagementSetup,
      BitemporalDate.newOf(DASHBOARD_DATE, null as LocalDateTime))
    1 * dashboardRepository.markInProgress(dashboard.id)
  }

  def "should start market data workflow dashboard for market data"() {
    setup:
    def dashboard = marketDataDashboard()
    dashboard.startedAt = null
    1 * dashboardRepository.dashboard(dashboard.id) >> Either.right(dashboard)
    workflowService.marketDataDashboardExists(dashboard.id) >> false

    when:
    def result = service.start(dashboard.id, true)

    then:
    result.isRight()
    1 * workflowService.startMarketDataDashboard(dashboard.id, dashboard.mdExceptionManagementSetup,
      BitemporalDate.newOf(DASHBOARD_DATE, null as LocalDateTime))
    1 * dashboardRepository.markInProgress(dashboard.id)
  }

  def "should fail start dashboard when steps already exists"() {
    setup:
    def dashboard = marketDataDashboard()
    1 * dashboardRepository.dashboard(dashboard.id) >> Either.right(dashboard)
    processor.getMdSteps(dashboard) >> marketDataDashboardSteps()

    when:
    def result = service.start(dashboard.id, false)

    then:
    result.isLeft()

    def error = result.left().getOrNull() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "Dashboard is already started"
  }

  def "should fail start workflow dashboard when it already exists"() {
    setup:
    def dashboard = valuationDataDashboard()
    1 * dashboardRepository.dashboard(dashboard.id) >> Either.right(dashboard)
    workflowService.valuationDataDashboardExists(dashboard.id) >> true

    when:
    def result = service.start(dashboard.id, true)

    then:
    result.isLeft()

    def error = result.left().getOrNull() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED
    error.description == "Dashboard is already started"
  }
}
