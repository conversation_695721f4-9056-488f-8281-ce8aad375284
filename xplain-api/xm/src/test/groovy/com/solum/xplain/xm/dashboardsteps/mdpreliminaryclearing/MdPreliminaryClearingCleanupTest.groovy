package com.solum.xplain.xm.dashboardsteps.mdpreliminaryclearing


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID

import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository
import spock.lang.Specification

class MdPreliminaryClearingCleanupTest extends Specification {
  TaskExecutionRepository repository = Mock()
  DashboardEntryRepository entryRepository = Mock()
  MdPreliminaryClearingCleanup stepCleanup = new MdPreliminaryClearingCleanup(repository, entryRepository)

  def "on execution should invoke repository"() {
    when:
    stepCleanup.execute(MD_DASHBOARD_ID)

    then:
    1 * repository.preliminaryDeleted(MD_DASHBOARD_ID)
    1 * entryRepository.deleteMdEntries(MD_DASHBOARD_ID, DashboardStep.MD_PRELIMINARY_CLEARING)
  }
}
