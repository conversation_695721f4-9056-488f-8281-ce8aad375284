package com.solum.xplain.xm.dashboards

import static io.atlassian.fugue.Either.right
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*

import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.enums.DashboardType
import com.solum.xplain.xm.dashboards.repository.DashboardRepository
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus
import com.solum.xplain.xm.excmngmt.process.InstrumentResultOverlayQueryRepository
import com.solum.xplain.xm.excmngmt.process.InstrumentResultPreliminaryQueryRepository
import com.solum.xplain.xm.excmngmt.process.data.*
import com.solum.xplain.xm.excmngmt.process.enums.BreakProviderType
import com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType
import com.solum.xplain.xm.excmngmt.process.form.ResolutionForm
import com.solum.xplain.xm.excmngmt.processipv.IpvTradeResultOverlayQueryRepository
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultResolution
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak
import com.solum.xplain.xm.excmngmt.processipv.enums.IpvBreakProviderType
import com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType
import com.solum.xplain.xm.excmngmt.processipv.resolution.data.TradeResultResolutionSubTypeReference
import com.solum.xplain.xm.excmngmt.rules.value.MeasureType
import com.solum.xplain.xm.excmngmt.rules.value.Operator
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType
import com.solum.xplain.xm.excmngmt.value.EntryResultStatusHistory
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Month
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
/**
 * This test class is also testing the dto mappings. while it doesn't test all the fields, it does try to go as deep as possible on the returned object.
 */
@WebMvcTest(controllers = [DashboardAuditableBreakTestsResultController])
@MockMvcConfiguration
@Import(DashboardAuditableBreakTestsResultService)
@ComponentScan(basePackages = "com.solum.xplain.xm.dashboards.views.auditablebreaktest.mappers")
class DashboardAuditableBreakTestsResultControllerTest extends IntegrationSpecification {

  @Autowired
  private MockMvc mockMvc

  @SpringBean
  DashboardRepository dashboardRepository = Mock()
  @SpringBean
  private final InstrumentResultPreliminaryQueryRepository instrumentResultPreliminaryQueryRepository = Mock()
  @SpringBean
  private final InstrumentResultOverlayQueryRepository instrumentResultOverlayQueryRepository = Mock()
  @SpringBean
  private final IpvTradeResultOverlayQueryRepository ipvTradeResultOverlayQueryRepository = Mock()

  static def FEB_3 = LocalDate.of(2021, Month.FEBRUARY, 3)

  def "should return only hasBreak = true (md/preliminary)"() {
    given:
    def mdgId = ObjectId.get().toString()
    def janDashboardId = ObjectId.get().toString()
    def febDashboardId = ObjectId.get().toString()
    def taskId = "superTaskId"
    def preliminaries = [bigPrelimResult(taskId, mdgId, FEB_3, "I1", 0.0404, 0.04015, febDashboardId),]

    dashboardRepository.dashboard(febDashboardId) >> right(new Dashboard(id: febDashboardId, type: DashboardType.MARKET_DATA))
    instrumentResultPreliminaryQueryRepository.findAllByDashboardIdAndHasBreaksIsTrue(febDashboardId, _) >> preliminaries

    expect:
    mockMvc.perform(get("/dashboard/${febDashboardId}/audit-breaks/preliminary")
      .contentType(MediaType.APPLICATION_JSON))
      .andExpect(status().isOk())
      .andExpect(content().contentType("application/json"))
      .andExpect(jsonPath('$.content[0].instrument.key').value("I1"))
      .andExpect(jsonPath('$.content[0].instrument.assetClassGroup').value("CREDIT"))
      .andExpect(jsonPath('$.content[0].instrument.assetClass').value("CDS"))
      .andExpect(jsonPath('$.content[0].events.length()').value(2))
      .andExpect(jsonPath('$.content[0].events[0].modifiedBy.userId').value("blih"))
      .andExpect(jsonPath('$.content[0].events[0].modifiedBy.username').value("bloh"))
      .andExpect(jsonPath('$.content[0].events[0].modifiedBy.name').value("blah"))
      .andExpect(jsonPath('$.content[0].events[0].comment').value("real approval comment"))
      .andExpect(jsonPath('$.content[0].events[0].status').value("VERIFIED"))
      .andExpect(jsonPath('$.content[0].events[1].modifiedBy.userId').value("userId"))
      .andExpect(jsonPath('$.content[0].events[1].modifiedBy.username').value("username"))
      .andExpect(jsonPath('$.content[0].events[1].modifiedBy.name').value("my name"))
      .andExpect(jsonPath('$.content[0].events[1].comment').value("resolution coms"))
      .andExpect(jsonPath('$.content[0].events[1].status').value("WAITING_APPROVAL"))
      .andExpect(jsonPath('$.content[0].breakTests[0].name').value("testName"))
      .andExpect(jsonPath('$.content[0].breakTests[0].type').value("Stuff"))
      .andExpect(jsonPath('$.content[0].breakTests[0].measureType').value("ABSOLUTE_DIFF"))
      .andExpect(jsonPath('$.content[0].breakTests[0].operator').value("GT"))
      .andExpect(jsonPath('$.content[0].breakTests[0].providerValue.value').value(1))
      .andExpect(jsonPath('$.content[0].taskId').value("superTaskId"))
      .andExpect(jsonPath('$.content[0].resolution.resolutionType').value("OVERRIDE_USER"))
  }


  def "should return only hasBreak = true (md/overlay)"() {
    given:

    def mdgId = ObjectId.get().toString()
    def febDashboardId = ObjectId.get().toString()
    def taskId = "superTaskId"
    def overlays = [bigOverlayResult(taskId, mdgId, FEB_3, "I1", 0.0404, 0.04015, febDashboardId),]

    dashboardRepository.dashboard(febDashboardId) >> right(new Dashboard(id: febDashboardId, type: DashboardType.MARKET_DATA))
    instrumentResultOverlayQueryRepository.findAllByDashboardIdAndHasBreaksIsTrue(febDashboardId, _) >> overlays

    expect:
    mockMvc.perform(get("/dashboard/${febDashboardId}/audit-breaks/overlay")
      .contentType(MediaType.APPLICATION_JSON))
      .andExpect(status().isOk())
      .andExpect(content().contentType("application/json"))
      .andExpect(jsonPath('$.content[0].instrument.key').value("I1"))
      .andExpect(jsonPath('$.content[0].instrument.assetClassGroup').value("CREDIT"))
      .andExpect(jsonPath('$.content[0].instrument.assetClass').value("CDS"))
      .andExpect(jsonPath('$.content[0].events.length()').value(2))
      .andExpect(jsonPath('$.content[0].events[0].modifiedBy.userId').value("blih"))
      .andExpect(jsonPath('$.content[0].events[0].modifiedBy.username').value("bloh"))
      .andExpect(jsonPath('$.content[0].events[0].modifiedBy.name').value("blah"))
      .andExpect(jsonPath('$.content[0].events[0].comment').value("real approval comment"))
      .andExpect(jsonPath('$.content[0].events[0].status').value("VERIFIED"))
      .andExpect(jsonPath('$.content[0].events[1].modifiedBy.userId').value("userId"))
      .andExpect(jsonPath('$.content[0].events[1].modifiedBy.username').value("username"))
      .andExpect(jsonPath('$.content[0].events[1].modifiedBy.name').value("my name"))
      .andExpect(jsonPath('$.content[0].events[1].comment').value("resolution coms"))
      .andExpect(jsonPath('$.content[0].events[1].status').value("WAITING_APPROVAL"))
      .andExpect(jsonPath('$.content[0].breakTests[0].name').value("testName"))
      .andExpect(jsonPath('$.content[0].breakTests[0].type').value("Stuff"))
      .andExpect(jsonPath('$.content[0].breakTests[0].measureType').value("ABSOLUTE_DIFF"))
      .andExpect(jsonPath('$.content[0].breakTests[0].operator').value("GT"))
      .andExpect(jsonPath('$.content[0].breakTests[0].providerValue.value').value(1))
      .andExpect(jsonPath('$.content[0].taskId').value("superTaskId"))
      .andExpect(jsonPath('$.content[0].resolution.resolutionType').value("OVERRIDE_USER"))
  }

  def "should return only hasBreak = true (vd/overlay)"() {
    given:

    def mdgId = ObjectId.get().toString()
    def febDashboardId = ObjectId.get().toString()
    def taskId = "superTaskId"
    def overlays = [bigIpvOverlayResult(taskId, FEB_3, "I1", febDashboardId),]

    dashboardRepository.dashboard(febDashboardId) >> right(new Dashboard(id: febDashboardId, type: DashboardType.VALUATION_DATA))
    ipvTradeResultOverlayQueryRepository.findAllByDashboardIdAndHasBreaksIsTrue(febDashboardId, _) >> overlays

    expect:
    mockMvc.perform(get("/dashboard/${febDashboardId}/audit-breaks/overlay")
      .contentType(MediaType.APPLICATION_JSON))
      .andExpect(status().isOk())
      .andExpect(content().contentType("application/json"))
      .andExpect(jsonPath('$.content[0].trade.key').value("I1"))
      .andExpect(jsonPath('$.content[0].trade.externalCompanyId').value("company"))
      .andExpect(jsonPath('$.content[0].trade.externalTradeId').value("my trade id"))
      .andExpect(jsonPath('$.content[0].events.length()').value(2))
      .andExpect(jsonPath('$.content[0].events[0].modifiedBy.userId').value("blih"))
      .andExpect(jsonPath('$.content[0].events[0].modifiedBy.username').value("bloh"))
      .andExpect(jsonPath('$.content[0].events[0].modifiedBy.name').value("blah"))
      .andExpect(jsonPath('$.content[0].events[0].comment').value("real approval comment"))
      .andExpect(jsonPath('$.content[0].events[0].status').value("VERIFIED"))
      .andExpect(jsonPath('$.content[0].events[1].modifiedBy.userId').value("userId"))
      .andExpect(jsonPath('$.content[0].events[1].modifiedBy.username').value("username"))
      .andExpect(jsonPath('$.content[0].events[1].modifiedBy.name').value("my name"))
      .andExpect(jsonPath('$.content[0].events[1].comment').value("resolution coms"))
      .andExpect(jsonPath('$.content[0].events[1].status').value("WAITING_APPROVAL"))
      .andExpect(jsonPath('$.content[0].breakTests[0].name').value("testName"))
      .andExpect(jsonPath('$.content[0].breakTests[0].type').value("Stuff"))
      .andExpect(jsonPath('$.content[0].breakTests[0].measureType').value("ABSOLUTE_DIFF"))
      .andExpect(jsonPath('$.content[0].breakTests[0].operator').value("GT"))
      .andExpect(jsonPath('$.content[0].breakTests[0].providerValue.value').value(1))
      .andExpect(jsonPath('$.content[0].taskId').value("superTaskId"))
      .andExpect(jsonPath('$.content[0].resolution.resolutionType').value("OVERRIDE_USER"))
  }

  private static bigOverlayResult(String taskId, String mdgId, LocalDate resultDate, String instrumentKey, BigDecimal value, BigDecimal previousValue, String dashboardId = null) {
    return new InstrumentResultOverlay(
      id: ObjectId.get().toString(),
      taskId: taskId,
      marketDataGroupId: mdgId,
      dashboardId: dashboardId,
      valuationDate: resultDate,
      modifiedAt: LocalDateTime.of(2023, 1, 1, 1, 1, 1, 1),
      modifiedBy: new AuditUser("blih", "bloh", "blah"),
      status: EntryResultStatus.VERIFIED,
      instrument: new Instrument(key: instrumentKey, instrumentType: CoreInstrumentType.CDS, assetClass: CoreAssetClass.CDS, assetClassGroup: CoreAssetGroup.CREDIT),
      resolution: InstrumentResultResolution.newOf(
      new ResolutionForm(
      InstrumentResultResolutionType.OVERRIDE_USER,
      BigDecimal.valueOf(123.0),
      "real approval comment"
      ),
      null),
      hasBreaks: true,
      breakTests: [
        new InstrumentResultBreak(
        breakTestName: "testName",
        breakTestType: "Stuff",
        measureType: MeasureType.ABSOLUTE_DIFF,
        operator: Operator.GT,
        threshold: BigDecimal.valueOf(1),
        providerType: BreakProviderType.PRIMARY,
        providerValue: EntryResultBreakByProvider.ofCalculationOnly(BigDecimal.valueOf(1)),
        parentBreakTestName: "a")
      ],
      previousStatuses: [
        new EntryResultStatusHistory(
        modifiedBy: new AuditUser("userId", "username", "my name"),
        modifiedAt: LocalDateTime.of(2022, 1, 1, 1, 1, 1, 1),
        status: EntryResultStatus.WAITING_APPROVAL,
        resolutionComment:"resolution coms",
        approvalComment: "approval coms",)
      ])
  }

  private static bigIpvOverlayResult(String taskId, LocalDate resultDate, String instrumentKey, String dashboardId = null) {
    return new IpvTradeResultOverlay(
      id: ObjectId.get().toString(),
      taskId: taskId,
      dashboardId: dashboardId,
      valuationDate: resultDate,
      modifiedAt: LocalDateTime.of(2023, 1, 1, 1, 1, 1, 1),
      modifiedBy: new AuditUser("blih", "bloh", "blah"),
      status: EntryResultStatus.VERIFIED,
      trade: new Trade(key: instrumentKey, externalCompanyId: "company", externalTradeId: "my trade id"),
      resolution: IpvTradeResultResolution.newOf(
      new com.solum.xplain.xm.excmngmt.processipv.form.ResolutionForm(
      TradeResultResolutionType.OVERRIDE_USER,
      TradeResultResolutionSubTypeReference.of("test"),
      BigDecimal.valueOf(123.0),

      "real approval comment"
      ),
      null),
      hasBreaks: true,
      breakTests: [
        new TradeResultBreak(
        breakTestName: "testName",
        breakTestType: "Stuff",
        measureType: IpvMeasureType.ABSOLUTE_DIFF,
        operator: Operator.GT,
        threshold: BigDecimal.valueOf(1),
        providerType: IpvBreakProviderType.PRIMARY,
        providerValue: EntryResultBreakByProvider.ofCalculationOnly(BigDecimal.valueOf(1)),
        parentBreakTestName: "a")
      ],
      previousStatuses: [
        new EntryResultStatusHistory(
        modifiedBy: new AuditUser("userId", "username", "my name"),
        modifiedAt: LocalDateTime.of(2022, 1, 1, 1, 1, 1, 1),
        status: EntryResultStatus.WAITING_APPROVAL,
        resolutionComment:"resolution coms",
        approvalComment: "approval coms",)
      ])
  }

  private static bigPrelimResult(String taskId, String mdgId, LocalDate resultDate, String instrumentKey, BigDecimal value, BigDecimal previousValue, String dashboardId = null) {
    return new InstrumentResultPreliminary(
      id: ObjectId.get().toString(),
      taskId: taskId,
      marketDataGroupId: mdgId,
      dashboardId: dashboardId,
      valuationDate: resultDate,
      modifiedAt: LocalDateTime.of(2023, 1, 1, 1, 1, 1, 1),
      modifiedBy: new AuditUser("blih", "bloh", "blah"),
      status: EntryResultStatus.VERIFIED,
      instrument: new Instrument(key: instrumentKey, instrumentType: CoreInstrumentType.CDS, assetClass: CoreAssetClass.CDS, assetClassGroup: CoreAssetGroup.CREDIT),
      resolution: InstrumentResultResolution.newOf(
      new ResolutionForm(
      InstrumentResultResolutionType.OVERRIDE_USER,
      BigDecimal.valueOf(123.0),

      "real approval comment"
      ),
      null),
      hasBreaks: true,
      breakTests: [
        new InstrumentResultBreak(
        breakTestName: "testName",
        breakTestType: "Stuff",
        measureType: MeasureType.ABSOLUTE_DIFF,
        operator: Operator.GT,
        threshold: BigDecimal.valueOf(1),
        providerType: BreakProviderType.PRIMARY,
        providerValue: EntryResultBreakByProvider.ofCalculationOnly(BigDecimal.valueOf(1)),
        parentBreakTestName: "a")
      ],
      previousStatuses: [
        new EntryResultStatusHistory(
        modifiedBy: new AuditUser("userId", "username", "my name"),
        modifiedAt: LocalDateTime.of(2022, 1, 1, 1, 1, 1, 1),
        status: EntryResultStatus.WAITING_APPROVAL,
        resolutionComment:"resolution coms",
        approvalComment: "approval coms",)
      ])
  }
}
