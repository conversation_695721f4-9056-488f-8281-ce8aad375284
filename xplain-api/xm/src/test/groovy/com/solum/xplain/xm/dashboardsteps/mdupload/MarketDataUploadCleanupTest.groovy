package com.solum.xplain.xm.dashboardsteps.mdupload


import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID

import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository
import spock.lang.Specification

class MarketDataUploadCleanupTest extends Specification {
  DashboardEntryRepository entryRepository = Mock()
  MarketDataUploadCleanup stepCleanup = new MarketDataUploadCleanup(entryRepository)

  def "on execution should invoke repository"() {
    when:
    stepCleanup.execute(MD_DASHBOARD_ID)

    then:
    1 * entryRepository.deleteMdEntries(MD_DASHBOARD_ID, DashboardStep.MARKET_DATA_UPLOAD)
  }
}
