package com.solum.xplain.xm.excmngmt.processipv.form;

import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springdoc.core.annotations.ParameterObject;

@Data
@ParameterObject
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TradeResultBreaksFilterForm extends ResultDisplayFilterForm {

  @NotNull @Valid private TradeFilterForm filter;

  public TradeResultBreaksFilterForm(
      @NotNull Boolean onlyCurvesWithBreaks,
      @NotNull Boolean includeHeld,
      TradeFilterForm filter,
      @NotNull Boolean onboardingOnly) {
    super(onlyCurvesWithBreaks, includeHeld, onboardingOnly);
    this.filter = filter;
  }
}
