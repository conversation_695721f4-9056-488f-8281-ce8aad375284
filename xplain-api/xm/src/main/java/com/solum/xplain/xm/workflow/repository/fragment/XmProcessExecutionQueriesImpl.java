package com.solum.xplain.xm.workflow.repository.fragment;

import static com.solum.xplain.core.common.AggregateOptions.ALLOW_DISK_USE;
import static com.solum.xplain.core.common.AggregationUtils.AGGREGATION_VAR;
import static com.solum.xplain.core.common.AggregationUtils.AGGREGATION_VAR_REF;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.mongo.AggregateFieldGroupOperation.AggregateGroupField.expressionField;
import static com.solum.xplain.core.utils.mongo.MongoVariables.POSITIONAL_OPERATOR;
import static com.solum.xplain.core.utils.mongo.TernaryOperations.isNotNull;
import static com.solum.xplain.core.utils.mongo.TernaryOperations.isNull;
import static com.solum.xplain.xm.excmngmt.ExceptionManagementBreakCountUtils.groupForBreakCounts;
import static com.solum.xplain.xm.excmngmt.ExceptionManagementBreakCountUtils.projectBaseCounts;
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.HOLD;
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.REJECTED;
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_APPROVAL;
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_RESOLUTION;
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_SUBMISSION;
import static com.solum.xplain.xm.excmngmt.process.ExceptionManagementProjections.instrumentPreliminaryResultViewProjection;
import static com.solum.xplain.xm.excmngmt.process.ExceptionManagementProjections.preliminaryNonRequiredDataView;
import static com.solum.xplain.xm.excmngmt.process.ExceptionManagementProjections.wfInstrumentOverlayResultViewProjection;
import static com.solum.xplain.xm.excmngmt.process.ExceptionManagementProjections.wfInstrumentPreliminaryResultViewProjection;
import static com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementProjections.wfIpvTradeOverlayResultViewProjection;
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.VD_XM_PROCESS_ID;
import static com.solum.xplain.xm.workflow.repository.fragment.InstrumentResultPreliminaryCriteriaMapper.forInstrumentResultPreliminary;
import static com.solum.xplain.xm.workflow.repository.fragment.InstrumentResultPreliminaryCriteriaMapper.forNonRequiredProviderData;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.addFields;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.lookup;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;
import static org.springframework.data.mongodb.core.aggregation.ArrayOperators.Reduce.Variable.THIS;
import static org.springframework.data.mongodb.core.aggregation.ArrayOperators.Reduce.Variable.VALUE;
import static org.springframework.data.mongodb.core.aggregation.ArrayOperators.arrayOf;
import static org.springframework.data.mongodb.core.aggregation.BooleanOperators.And.and;
import static org.springframework.data.mongodb.core.aggregation.BooleanOperators.Or.or;
import static org.springframework.data.mongodb.core.aggregation.ComparisonOperators.valueOf;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.Switch.CaseOperator;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.ifNull;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.switchCases;
import static org.springframework.data.mongodb.core.aggregation.ConditionalOperators.when;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID_REF;
import static org.springframework.data.mongodb.core.aggregation.Fields.field;
import static org.springframework.data.mongodb.core.aggregation.UnionWithOperation.unionWith;
import static org.springframework.data.mongodb.core.aggregation.UnsetOperation.unset;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableList.Builder;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.utils.mongo.AggregateFieldGroupOperation;
import com.solum.xplain.workflow.entity.ProcessExecution;
import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.workflow.value.WorkflowStatus;
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.process.data.Instrument;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary;
import com.solum.xplain.xm.excmngmt.process.data.NonRequiredProviderData;
import com.solum.xplain.xm.excmngmt.process.view.BreakCountView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView;
import com.solum.xplain.xm.excmngmt.process.view.chart.TaskChartDataEntry;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeResultBreakView;
import com.solum.xplain.xm.excmngmt.processipv.view.chart.IpvTaskChartDataEntry;
import com.solum.xplain.xm.tasks.entity.IpvTasksDefinition;
import com.solum.xplain.xm.tasks.entity.TasksDefinition;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.workflow.MdXmWorkflowProvider;
import com.solum.xplain.xm.workflow.VdXmWorkflowProvider;
import com.solum.xplain.xm.workflow.state.MdOverlayContext;
import com.solum.xplain.xm.workflow.state.MdOverlayState;
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext;
import com.solum.xplain.xm.workflow.state.MdPreliminaryState;
import com.solum.xplain.xm.workflow.state.VdPhaseContext;
import com.solum.xplain.xm.workflow.state.VdPhaseState;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.bson.BsonNull;
import org.bson.Document;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationExpression;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.aggregation.Fields;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class XmProcessExecutionQueriesImpl implements XmProcessExecutionQueries {
  public static final List<EntryResultStatus> PENDING_STATUSES =
      List.of(WAITING_RESOLUTION, REJECTED);
  public static final List<EntryResultStatus> RESOLVED_STATUSES =
      List.of(EntryResultStatus.VERIFIED, WAITING_SUBMISSION, WAITING_APPROVAL);
  public static final List<EntryResultStatus> APPROVED_STATUSES =
      List.of(EntryResultStatus.VERIFIED, WAITING_SUBMISSION);
  public static final String STEP_FIELD = "step";
  private static final String REF = POSITIONAL_OPERATOR;
  private final MongoOperations mongoOperations;
  private final TaskCriteriaParser taskCriteriaParser = new TaskCriteriaParser();
  private final TaskCriteriaMapper mapper = WorkflowTaskCriteriaMapper.forProcessExecution();
  private final IpvTaskCriteriaMapper ipvTaskMapper =
      WorkflowTaskCriteriaMapper.forProcessExecutionIpv();
  private final TaskCriteriaMapper instrumentResultPreliminaryCriteriaMapper =
      forInstrumentResultPreliminary();
  private final TaskCriteriaMapper nonRequiredProviderDataCriteriaMapper =
      forNonRequiredProviderData();

  /**
   * If the step exists and is in FINALIZING state then merge the outcome onto the process state and
   * update the status to either WAITING_APPROVAL or WAITING_SUBMISSION, so that we can see it in
   * the view and can submit. Also override the process status with the step status.
   */
  private static void addEffectiveStatusMerge(
      ImmutableList.Builder<AggregationOperation> operations) {
    operations.add(
        addFields()
            .addField(ProcessExecution.Fields.status)
            .withValue(REF + joinPaths(STEP_FIELD, StepInstance.Fields.status))
            .addField(ProcessExecution.Fields.currentState)
            .withValue(
                switchCases(
                        CaseOperator.when(
                                valueOf(joinPaths(STEP_FIELD, StepInstance.Fields.status))
                                    .equalToValue(WorkflowStatus.FINALIZING))
                            .then(mergeStepOutcomeAndFutureStatusIntoCurrentState()),
                        CaseOperator.when(
                                valueOf(joinPaths(STEP_FIELD, StepInstance.Fields.status))
                                    .equalToValue(WorkflowStatus.HELD))
                            .then(mergeStatusInToCurrentState(HOLD)))
                    .defaultTo(field(ProcessExecution.Fields.currentState)))
            .build());
  }

  private static AggregationExpression mergeStepOutcomeAndFutureStatusIntoCurrentState() {
    String currentStateRef = POSITIONAL_OPERATOR + ProcessExecution.Fields.currentState;
    String outcomeRef = joinPaths(POSITIONAL_OPERATOR + STEP_FIELD, StepInstance.Fields.outcome);
    ConditionalOperators.Cond statusCond =
        when(arrayOf(PENDING_STATUSES)
                .containsValue(
                    joinPaths(
                        REF + ProcessExecution.Fields.currentState,
                        VdPhaseState.Fields.entryStatus)))
            .then(WAITING_APPROVAL)
            .otherwise(WAITING_SUBMISSION);
    return context ->
        new Document()
            .append(
                "$mergeObjects",
                List.of(
                    currentStateRef,
                    outcomeRef,
                    new Document(VdPhaseState.Fields.entryStatus, statusCond.toDocument(context))));
  }

  private static AggregationExpression mergeStatusInToCurrentState(EntryResultStatus status) {
    String currentStateRef = POSITIONAL_OPERATOR + ProcessExecution.Fields.currentState;
    return context ->
        new Document()
            .append(
                "$mergeObjects",
                List.of(currentStateRef, new Document(VdPhaseState.Fields.entryStatus, status)));
  }

  private static boolean addStepLookupIfNecessary(
      ParsedTaskCriteria parsedTaskCriteria,
      Criteria extraStepCriteria,
      ImmutableList.Builder<AggregationOperation> operations) {
    List<String> stepIds = parsedTaskCriteria.getStepIds();
    if (!stepIds.isEmpty()) {
      addStepIdLookup(extraStepCriteria, operations, stepIds);
    }
    return !stepIds.isEmpty();
  }

  private static boolean addStepLookupIfNecessary(
      IpvParsedTaskCriteria parsedTaskCriteria,
      Criteria extraStepCriteria,
      ImmutableList.Builder<AggregationOperation> operations) {
    List<String> stepIds = parsedTaskCriteria.getStepIds();
    if (!stepIds.isEmpty()) {
      addStepIdLookup(extraStepCriteria, operations, stepIds);
    }
    return !stepIds.isEmpty();
  }

  private static void addStepIdLookup(
      Criteria extraStepCriteria, Builder<AggregationOperation> operations, List<String> stepIds) {
    operations.add(
        lookup()
            .from(StepInstance.STEP_INSTANCE_COLLECTION)
            .localField(UNDERSCORE_ID)
            .foreignField(StepInstance.Fields.executionId)
            .pipeline(
                match(
                    where(StepInstance.Fields.reportable)
                        .is(true)
                        .and(StepInstance.Fields.stepId)
                        .in(stepIds)),
                match(extraStepCriteria),
                sort(Sort.Direction.DESC, StepInstance.Fields.startedAt),
                group(StepInstance.Fields.stepId)
                    .first(StepInstance.Fields.status)
                    .as(StepInstance.Fields.status)
                    .first(StepInstance.Fields.outcome)
                    .as(StepInstance.Fields.outcome),
                project(StepInstance.Fields.outcome, StepInstance.Fields.status)
                    .and(UNDERSCORE_ID_REF)
                    .as(StepInstance.Fields.stepId)
                    .andExclude(UNDERSCORE_ID))
            .as(STEP_FIELD));
    operations.add(unwind(STEP_FIELD, true));
  }

  public BreakCountView getVdBreakCountView(
      List<String> taskGroups,
      Map<IpvExceptionManagementPhase, IpvTasksDefinition> tasksDefinitions) {
    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();

    operations.add(
        match(
            where(ProcessExecution.Fields.processId)
                .is(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID)));

    IpvParsedTaskCriteria parsedTaskCriteria =
        taskCriteriaParser.parseIpvTaskSpecifications(taskGroups, tasksDefinitions);
    boolean stepIdSpecified =
        addStepLookupIfNecessary(parsedTaskCriteria, new Criteria(), operations);
    if (stepIdSpecified) {
      operations.add(
          match(
              where(joinPaths(STEP_FIELD, StepInstance.Fields.status))
                  .not()
                  .in(WorkflowStatus.ERROR, WorkflowStatus.INTERRUPTED)));
    }
    operations.add(parsedTaskCriteria.taskGroupMatch(ipvTaskMapper));
    operations.addAll(
        buildBreakCountViewOperations(
            stepIdSpecified,
            joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.hasBreak),
            joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.breakTestResults),
            joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.entryStatus)));

    return mongoOperations
        .aggregate(
            Aggregation.newAggregation(ProcessExecution.class, operations.build())
                .withOptions(ALLOW_DISK_USE),
            BreakCountView.class)
        .getUniqueMappedResult();
  }

  @Override
  public BreakCountView getMdPreliminaryBreakCountView(
      List<String> taskGroups,
      Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions,
      boolean breaksOnly) {

    var breakCountView =
        getBreakCountView(
            MdXmWorkflowProvider.MD_XM_PRELIMINARY_PROCESS_ID,
            taskGroups,
            tasksDefinitions,
            joinPaths(ProcessExecution.Fields.context, MdPreliminaryContext.Fields.hasBreak),
            joinPaths(
                ProcessExecution.Fields.context, MdPreliminaryContext.Fields.breakTestResults),
            joinPaths(ProcessExecution.Fields.currentState, MdPreliminaryState.Fields.entryStatus));
    if (!breaksOnly) {
      ParsedTaskCriteria parsedTaskCriteria =
          taskCriteriaParser.parseTaskSpecifications(taskGroups, tasksDefinitions);
      var unbrokenBreakDownView =
          mongoOperations
              .aggregate(
                  newAggregation(
                      InstrumentResultPreliminary.class,
                      List.of(
                          match(where(InstrumentResultPreliminary.Fields.hasBreaks).is(false)),
                          parsedTaskCriteria.taskGroupMatch(
                              instrumentResultPreliminaryCriteriaMapper),
                          projectBaseCounts(InstrumentResultPreliminary.Fields.breakTests),
                          groupForBreakCounts())),
                  BreakCountView.class)
              .getUniqueMappedResult();
      // If everything is broken, then the count returns no rows or values.
      if (unbrokenBreakDownView == null) {
        return breakCountView;
      }
      unbrokenBreakDownView.setFinalizingCount(
          unbrokenBreakDownView.getResolvedCount() - unbrokenBreakDownView.getVerifiedCount());
      unbrokenBreakDownView.setHeldCount(0L);
      return BreakCountView.merge(breakCountView, unbrokenBreakDownView);
    }
    return breakCountView;
  }

  @Override
  public BreakCountView getMdOverlayBreakCountView(
      List<String> taskGroups, Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions) {
    return getBreakCountView(
        MdXmWorkflowProvider.MD_XM_OVERLAY_PROCESS_ID,
        taskGroups,
        tasksDefinitions,
        joinPaths(ProcessExecution.Fields.currentState, MdOverlayState.Fields.hasBreak),
        joinPaths(ProcessExecution.Fields.currentState, MdOverlayState.Fields.breakTestResults),
        joinPaths(ProcessExecution.Fields.currentState, MdOverlayState.Fields.entryStatus));
  }

  private BreakCountView getBreakCountView(
      String processId,
      List<String> taskGroups,
      Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions,
      String hasBreakField,
      String breakTestResultsField,
      String entryStatusField) {
    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();

    operations.add(match(where(ProcessExecution.Fields.processId).is(processId)));

    ParsedTaskCriteria parsedTaskCriteria =
        taskCriteriaParser.parseTaskSpecifications(taskGroups, tasksDefinitions);
    boolean stepIdSpecified =
        addStepLookupIfNecessary(parsedTaskCriteria, new Criteria(), operations);
    if (stepIdSpecified) {
      operations.add(
          match(
              where(joinPaths(STEP_FIELD, StepInstance.Fields.status))
                  .not()
                  .in(WorkflowStatus.ERROR, WorkflowStatus.INTERRUPTED)));
    }

    operations.add(parsedTaskCriteria.taskGroupMatch(mapper));
    operations.addAll(
        buildBreakCountViewOperations(
            stepIdSpecified, hasBreakField, breakTestResultsField, entryStatusField));

    return mongoOperations
        .aggregate(
            Aggregation.newAggregation(ProcessExecution.class, operations.build())
                .withOptions(ALLOW_DISK_USE),
            BreakCountView.class)
        .getUniqueMappedResult();
  }

  private List<AggregationOperation> buildBreakCountViewOperations(
      boolean stepIdSpecified,
      String hasBreakField,
      String breakTestResultsField,
      String entryStatusField) {
    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();

    // If the step exists and is in FINALIZING state then update the status to either
    // WAITING_APPROVAL or WAITING_SUBMISSION
    // so that the stats reflect that.
    if (stepIdSpecified) {
      operations.add(
          addFields()
              .addField(ProcessExecution.Fields.currentState)
              .withValue(
                  when(valueOf(joinPaths(STEP_FIELD, StepInstance.Fields.status))
                          .equalToValue(WorkflowStatus.FINALIZING))
                      .then(mergeStepOutcomeAndFutureStatusIntoCurrentState())
                      .otherwiseValueOf(ProcessExecution.Fields.currentState))
              .build());
    }
    operations.add(
        group()
            .first(ProcessExecution.Fields.rootBusinessKey)
            .as(BreakCountView.Fields.progressKey)
            .count()
            .as(BreakCountView.Fields.totalCount)
            .sum(when(hasBreakField).then(1).otherwise(0))
            .as(BreakCountView.Fields.breaksCount)
            .sum(
                arrayOf(
                        ifNull(
                                arrayOf(REF + breakTestResultsField)
                                    .filter()
                                    .as(AGGREGATION_VAR)
                                    .by(
                                        or(
                                            isNull(
                                                joinPaths(
                                                    AGGREGATION_VAR_REF,
                                                    TradeResultBreak.Fields.threshold)),
                                            isNotNull(
                                                joinPaths(
                                                    AGGREGATION_VAR_REF,
                                                    TradeResultBreak.Fields.providerValue,
                                                    EntryResultBreakByProvider.Fields.value)))))
                            .then(List.of()))
                    .length())
            .as(BreakCountView.Fields.appliedTestsCount)
            .sum(arrayOf(ifNull(breakTestResultsField).then(List.of())).length())
            .as(BreakCountView.Fields.totalTestsCount)
            .sum(
                when(and(
                        arrayOf(APPROVED_STATUSES).containsValue(REF + entryStatusField),
                        valueOf(hasBreakField).equalToValue(true)))
                    .then(1)
                    .otherwise(0))
            .as(BreakCountView.Fields.verifiedCount)
            .sum(
                when(and(
                        arrayOf(RESOLVED_STATUSES).containsValue(REF + entryStatusField),
                        valueOf(hasBreakField).equalToValue(true)))
                    .then(1)
                    .otherwise(0))
            .as(BreakCountView.Fields.resolvedCount)
            .sum(
                when(valueOf(joinPaths(STEP_FIELD, StepInstance.Fields.status))
                        .equalToValue(WorkflowStatus.FINALIZING))
                    .then(1)
                    .otherwise(0))
            .as(BreakCountView.Fields.finalizingCount)
            .sum(
                when(valueOf(
                            arrayOf(
                                    ifNull(
                                            arrayOf(REF + breakTestResultsField)
                                                .filter()
                                                .as(AGGREGATION_VAR)
                                                .by(
                                                    and(
                                                        valueOf(
                                                                joinPaths(
                                                                    AGGREGATION_VAR_REF,
                                                                    TradeResultBreak.Fields
                                                                        .onboardingTest))
                                                            .equalToValue(true),
                                                        valueOf(
                                                                joinPaths(
                                                                    AGGREGATION_VAR_REF,
                                                                    TradeResultBreak.Fields
                                                                        .providerValue,
                                                                    IpvTradeResultBreakView.Fields
                                                                        .triggered))
                                                            .equalToValue(true))))
                                        .then(List.of()))
                                .length())
                        .greaterThanValue(0))
                    .then(1)
                    .otherwise(0))
            .as(BreakCountView.Fields.onboardingCount)
            .sum(
                when(valueOf(joinPaths(STEP_FIELD, StepInstance.Fields.status))
                        .equalToValue(WorkflowStatus.HELD))
                    .then(1)
                    .otherwise(0))
            .as(BreakCountView.Fields.heldCount));

    return operations.build();
  }

  @Override
  public List<TaskChartDataEntry> getMdPreliminaryChartData(
      List<String> taskGroups, Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions) {

    return getMdChartData(
        MdXmWorkflowProvider.MD_XM_PRELIMINARY_PROCESS_ID,
        taskGroups,
        tasksDefinitions,
        ProcessExecution.Fields.context,
        MdPreliminaryContext.Fields.hasBreak,
        MdPreliminaryContext.Fields.instrument);
  }

  @Override
  public List<TaskChartDataEntry> getMdOverlayChartData(
      List<String> taskGroups, Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions) {

    return getMdChartData(
        MdXmWorkflowProvider.MD_XM_OVERLAY_PROCESS_ID,
        taskGroups,
        tasksDefinitions,
        ProcessExecution.Fields.currentState,
        MdOverlayState.Fields.hasBreak,
        MdOverlayContext.Fields.instrument);
  }

  /**
   * Common method to get chart data for MD processes.
   *
   * @param processId The process ID to filter by
   * @param taskGroups List of task groups
   * @param tasksDefinitions Map of task definitions
   * @param hasBreakPath Path to the hasBreak field
   * @param hasBreakField Field name for hasBreak
   * @param instrumentPath Path to the instrument field
   * @return List of TaskChartDataEntry objects
   */
  private List<TaskChartDataEntry> getMdChartData(
      String processId,
      List<String> taskGroups,
      Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions,
      String hasBreakPath,
      String hasBreakField,
      String instrumentPath) {

    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();
    operations.add(
        match(
            where(ProcessExecution.Fields.processId)
                .is(processId)
                .and(joinPaths(hasBreakPath, hasBreakField))
                .is(true)));

    ParsedTaskCriteria parsedTaskCriteria =
        taskCriteriaParser.parseTaskSpecifications(taskGroups, tasksDefinitions);
    operations.add(parsedTaskCriteria.taskGroupMatch(mapper));

    String instrumentFullPath = joinPaths(ProcessExecution.Fields.context, instrumentPath);

    var creditSectorRef =
        joinPaths(POSITIONAL_OPERATOR + instrumentFullPath, Instrument.Fields.sector);
    var currencyPairRef =
        joinPaths(POSITIONAL_OPERATOR + instrumentFullPath, Instrument.Fields.fxPair);
    var currencyRef =
        joinPaths(POSITIONAL_OPERATOR + instrumentFullPath, Instrument.Fields.currency);

    operations.add(
        new AggregateFieldGroupOperation(
            group().count().as(TaskChartDataEntry.Fields.count),
            Fields.from(
                field(
                    Instrument.Fields.assetClassGroup,
                    joinPaths(instrumentFullPath, Instrument.Fields.assetClassGroup)),
                field(
                    Instrument.Fields.assetClass,
                    joinPaths(instrumentFullPath, Instrument.Fields.assetClass)),
                expressionField(
                    Instrument.Fields.underlying,
                    when(creditSectorRef)
                        .then(creditSectorRef)
                        .otherwise(
                            when(currencyPairRef).then(currencyPairRef).otherwise(currencyRef))))));

    operations.add(
        projectAndGroupLevel(
            Instrument.Fields.underlying,
            TaskChartDataEntry.Fields.count,
            TaskChartDataEntry.Fields.children,
            TaskChartDataEntry.Fields.name,
            Instrument.Fields.assetClass,
            Instrument.Fields.assetClassGroup));
    operations.add(
        projectAndGroupLevel(
            Instrument.Fields.assetClass,
            TaskChartDataEntry.Fields.count,
            TaskChartDataEntry.Fields.children,
            TaskChartDataEntry.Fields.name,
            Instrument.Fields.assetClassGroup));
    operations.add(
        project(TaskChartDataEntry.Fields.count, TaskChartDataEntry.Fields.children)
            .and(UNDERSCORE_ID_REF)
            .as(TaskChartDataEntry.Fields.name)
            .andExclude(UNDERSCORE_ID));

    return mongoOperations
        .aggregate(
            Aggregation.newAggregation(ProcessExecution.class, operations.build())
                .withOptions(ALLOW_DISK_USE),
            TaskChartDataEntry.class)
        .getMappedResults();
  }

  @Override
  public List<IpvTaskChartDataEntry> getVdChartData(
      List<String> taskGroups,
      Map<IpvExceptionManagementPhase, IpvTasksDefinition> tasksDefinitions) {
    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();
    operations.add(
        match(
            where(ProcessExecution.Fields.processId)
                .is(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID)
                .and(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.hasBreak))
                .is(true)));

    IpvParsedTaskCriteria parsedTaskCriteria =
        taskCriteriaParser.parseIpvTaskSpecifications(taskGroups, tasksDefinitions);
    operations.add(parsedTaskCriteria.taskGroupMatch(ipvTaskMapper));

    var creditSectorRef =
        joinPaths(
            POSITIONAL_OPERATOR + ProcessExecution.Fields.context,
            VdPhaseContext.Fields.trade,
            Trade.Fields.creditSector);
    var currencyPairRef =
        joinPaths(
            POSITIONAL_OPERATOR + ProcessExecution.Fields.context,
            VdPhaseContext.Fields.trade,
            Trade.Fields.currencyPair);
    var currencyRef =
        joinPaths(
            POSITIONAL_OPERATOR + ProcessExecution.Fields.context,
            VdPhaseContext.Fields.trade,
            Trade.Fields.currency);

    operations.add(
        new AggregateFieldGroupOperation(
            group().count().as(IpvTaskChartDataEntry.Fields.count),
            Fields.from(
                field(
                    Trade.Fields.productGroup,
                    joinPaths(
                        ProcessExecution.Fields.context,
                        VdPhaseContext.Fields.trade,
                        Trade.Fields.productGroup)),
                field(
                    Trade.Fields.productType,
                    joinPaths(
                        ProcessExecution.Fields.context,
                        VdPhaseContext.Fields.trade,
                        Trade.Fields.productType)),
                expressionField(
                    Trade.Fields.underlying,
                    when(creditSectorRef)
                        .then(creditSectorRef)
                        .otherwise(
                            when(currencyPairRef).then(currencyPairRef).otherwise(currencyRef))),
                expressionField(
                    EntryResultBreakByProvider.Fields.triggeredThresholdLevel,
                    ArrayOperators.arrayOf(
                            joinPaths(
                                ProcessExecution.Fields.currentState,
                                VdPhaseState.Fields.breakTestResults,
                                TradeResultBreak.Fields.providerValue,
                                EntryResultBreakByProvider.Fields.triggeredThresholdLevel))
                        .reduce(
                            when(valueOf(THIS.getTarget()).greaterThan(VALUE.getTarget()))
                                .thenValueOf(THIS.getTarget())
                                .otherwiseValueOf(VALUE.getTarget()))
                        .startingWith(BsonNull.VALUE)))));

    operations.add(
        projectAndGroupLevel(
            EntryResultBreakByProvider.Fields.triggeredThresholdLevel,
            IpvTaskChartDataEntry.Fields.count,
            IpvTaskChartDataEntry.Fields.children,
            IpvTaskChartDataEntry.Fields.name,
            Trade.Fields.underlying,
            Trade.Fields.productType,
            Trade.Fields.productGroup));
    operations.add(
        projectAndGroupLevel(
            Trade.Fields.underlying,
            IpvTaskChartDataEntry.Fields.count,
            IpvTaskChartDataEntry.Fields.children,
            IpvTaskChartDataEntry.Fields.name,
            Trade.Fields.productType,
            Trade.Fields.productGroup));
    operations.add(
        projectAndGroupLevel(
            Trade.Fields.productType,
            IpvTaskChartDataEntry.Fields.count,
            IpvTaskChartDataEntry.Fields.children,
            IpvTaskChartDataEntry.Fields.name,
            Trade.Fields.productGroup));
    operations.add(
        project(IpvTaskChartDataEntry.Fields.count, IpvTaskChartDataEntry.Fields.children)
            .and(UNDERSCORE_ID_REF)
            .as(IpvTaskChartDataEntry.Fields.name)
            .andExclude(UNDERSCORE_ID));

    return mongoOperations
        .aggregate(
            Aggregation.newAggregation(ProcessExecution.class, operations.build())
                .withOptions(ALLOW_DISK_USE),
            IpvTaskChartDataEntry.class)
        .getMappedResults();
  }

  private AggregationOperation[] projectAndGroupLevel(
      String nameField,
      String countField,
      String childrenField,
      String nameTargetField,
      String... parentGroupFields) {
    ProjectionOperation projectionOperation =
        project(countField, childrenField)
            .and(joinPaths(UNDERSCORE_ID_REF, nameField))
            .as(nameTargetField)
            .andExclude(UNDERSCORE_ID);
    for (String parentGroupField : parentGroupFields) {
      projectionOperation =
          projectionOperation
              .and(joinPaths(UNDERSCORE_ID_REF, parentGroupField))
              .as(parentGroupField);
    }

    GroupOperation groupOperation =
        group(parentGroupFields)
            .push(
                ((AggregationExpression)
                    (context ->
                        new Document()
                            .append(nameTargetField, POSITIONAL_OPERATOR + nameTargetField)
                            .append(countField, POSITIONAL_OPERATOR + countField)
                            .append(childrenField, POSITIONAL_OPERATOR + childrenField))))
            .as(childrenField)
            .sum(countField)
            .as(countField);

    return new AggregationOperation[] {projectionOperation, groupOperation};
  }

  @Override
  public List<IpvTradeOverlayResultView> getIpvClearingViews(
      List<String> taskGroups,
      Map<IpvExceptionManagementPhase, IpvTasksDefinition> tasksDefinitions,
      boolean brokenOnly,
      boolean includeHeld,
      boolean onboardingOnly,
      Criteria postProjectionCriteria,
      Pageable page) {
    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();

    operations.add(
        match(
            where(ProcessExecution.Fields.processId)
                .is(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID)));

    IpvParsedTaskCriteria parsedTaskCriteria =
        taskCriteriaParser.parseIpvTaskSpecifications(taskGroups, tasksDefinitions);

    boolean stepIdSpecified =
        addStepLookupIfNecessary(
            parsedTaskCriteria,
            where(StepInstance.Fields.status)
                .in(WorkflowStatus.ACTIVE, WorkflowStatus.FINALIZING, WorkflowStatus.HELD),
            operations);

    operations.add(parsedTaskCriteria.taskGroupMatch(ipvTaskMapper));

    if (brokenOnly) {
      operations.add(
          match(
              where(joinPaths(ProcessExecution.Fields.currentState, VdPhaseState.Fields.hasBreak))
                  .is(true)));
    }

    if (!includeHeld) {
      operations.add(match(where(ProcessExecution.Fields.status).ne(WorkflowStatus.HELD)));
    }

    if (onboardingOnly) {
      operations.add(
          match(
              where(
                      joinPaths(
                          ProcessExecution.Fields.currentState,
                          VdPhaseState.Fields.breakTestResults))
                  .elemMatch(
                      Criteria.where(TradeResultBreak.Fields.onboardingTest)
                          .is(true)
                          .and(
                              joinPaths(
                                  TradeResultBreak.Fields.providerValue,
                                  IpvTradeResultBreakView.Fields.triggered))
                          .is(true))));
    }

    if (stepIdSpecified) {
      addEffectiveStatusMerge(operations);
    }

    operations.add(wfIpvTradeOverlayResultViewProjection());

    return aggregateWithFilterAndPaging(
        postProjectionCriteria,
        page,
        operations,
        IpvTradeOverlayResultView.class,
        IpvTradeOverlayResultView.Fields.id);
  }

  @Override
  public List<InstrumentPreliminaryResultView> getPreliminaryClearingViews(
      List<String> taskGroups,
      Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions,
      boolean brokenOnly,
      boolean includeHeld,
      Criteria postProjectionCriteria,
      Pageable page) {
    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();

    ParsedTaskCriteria parsedTaskCriteria =
        taskCriteriaParser.parseTaskSpecifications(taskGroups, tasksDefinitions);
    boolean stepIdSpecified =
        addStepLookupIfNecessary(
            parsedTaskCriteria,
            where(StepInstance.Fields.status)
                .in(WorkflowStatus.ACTIVE, WorkflowStatus.FINALIZING, WorkflowStatus.HELD),
            operations);

    operations.add(parsedTaskCriteria.taskGroupMatch(mapper));

    if (brokenOnly) {
      operations.add(
          match(
              where(
                      joinPaths(
                          ProcessExecution.Fields.context, MdPreliminaryContext.Fields.hasBreak))
                  .is(true)));
    }

    if (!includeHeld) {
      operations.add(match(where(ProcessExecution.Fields.status).ne(WorkflowStatus.HELD)));
    }

    if (stepIdSpecified) {
      addEffectiveStatusMerge(operations);
    }

    operations.add(wfInstrumentPreliminaryResultViewProjection());

    // Adding it as late as possible to not apply previous pipeline processing
    if (!brokenOnly) {
      operations.add(
          unionWith(InstrumentResultPreliminary.INSTRUMENT_RESULT_PRELIMINARY_COLLECTION)
              .pipeline(
                  match(where(InstrumentResultPreliminary.Fields.hasBreaks).is(false)),
                  parsedTaskCriteria.taskGroupMatch(instrumentResultPreliminaryCriteriaMapper),
                  instrumentPreliminaryResultViewProjection()));

      operations.add(
          unionWith(NonRequiredProviderData.NON_REQUIRED_PROVIDER_DATA_COLLECTION)
              .pipeline(
                  parsedTaskCriteria.taskGroupMatch(nonRequiredProviderDataCriteriaMapper),
                  preliminaryNonRequiredDataView()));
    }

    return aggregateWithFilterAndPaging(
        postProjectionCriteria,
        page,
        operations,
        InstrumentPreliminaryResultView.class,
        InstrumentPreliminaryResultView.Fields.id);
  }

  public List<InstrumentOverlayResultView> getOverlayClearingViews(
      List<String> taskGroups,
      Map<TaskExceptionManagementType, TasksDefinition> tasksDefinitions,
      boolean brokenOnly,
      boolean includeHeld,
      Criteria postProjectionCriteria,
      Pageable page) {
    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();

    ParsedTaskCriteria parsedTaskCriteria =
        taskCriteriaParser.parseTaskSpecifications(taskGroups, tasksDefinitions);
    boolean stepIdSpecified =
        addStepLookupIfNecessary(
            parsedTaskCriteria,
            where(StepInstance.Fields.status)
                .in(WorkflowStatus.ACTIVE, WorkflowStatus.FINALIZING, WorkflowStatus.HELD),
            operations);

    operations.add(parsedTaskCriteria.taskGroupMatch(mapper));

    if (brokenOnly) {
      operations.add(
          match(
              where(joinPaths(ProcessExecution.Fields.currentState, MdOverlayState.Fields.hasBreak))
                  .is(true)));
    }

    if (!includeHeld) {
      operations.add(match(where(ProcessExecution.Fields.status).ne(WorkflowStatus.HELD)));
    }

    if (stepIdSpecified) {
      addEffectiveStatusMerge(operations);
    }

    operations.add(wfInstrumentOverlayResultViewProjection());

    return aggregateWithFilterAndPaging(
        postProjectionCriteria,
        page,
        operations,
        InstrumentOverlayResultView.class,
        InstrumentOverlayResultView.Fields.id);
  }

  private <T> List<T> aggregateWithFilterAndPaging(
      Criteria postProjectionCriteria,
      Pageable page,
      ImmutableList.Builder<AggregationOperation> operations,
      Class<T> outputType,
      String idPath) {
    operations.add(match(postProjectionCriteria));
    operations.addAll(new ScrollSortOperations(page, idPath).build());

    return mongoOperations
        .aggregate(
            Aggregation.newAggregation(ProcessExecution.class, operations.build())
                .withOptions(ALLOW_DISK_USE),
            outputType)
        .getMappedResults();
  }

  @Override
  public List<ProcessExecution> findProcessExecutionsForTasks(
      List<String> taskGroups,
      Map<IpvExceptionManagementPhase, IpvTasksDefinition> tasksDefinitions,
      List<WorkflowStatus> excludeStatuses) {
    ImmutableList.Builder<AggregationOperation> operations = ImmutableList.builder();

    operations.add(
        match(
            where(ProcessExecution.Fields.processId)
                .is(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID)
                .and(ProcessExecution.Fields.status)
                .nin(excludeStatuses)));

    IpvParsedTaskCriteria parsedTaskCriteria =
        taskCriteriaParser.parseIpvTaskSpecifications(taskGroups, tasksDefinitions);

    operations.add(parsedTaskCriteria.taskGroupMatch(ipvTaskMapper));

    operations.add(unset(ProcessExecution.Fields.currentState));

    return mongoOperations
        .aggregate(
            Aggregation.newAggregation(ProcessExecution.class, operations.build())
                .withOptions(ALLOW_DISK_USE),
            ProcessExecution.class)
        .getMappedResults();
  }

  public Stream<ProcessExecution> findDashboardsByStateDate(LocalDate stateDate) {
    return mongoOperations.stream(
        query(
            where(ProcessExecution.Fields.processId)
                .is(VD_XM_PROCESS_ID)
                .and(
                    joinPaths(
                        ProcessExecution.Fields.context,
                        VdPhaseContext.Fields.stateDate,
                        BitemporalDate.Fields.actualDate))
                .is(stateDate)),
        ProcessExecution.class);
  }
}
