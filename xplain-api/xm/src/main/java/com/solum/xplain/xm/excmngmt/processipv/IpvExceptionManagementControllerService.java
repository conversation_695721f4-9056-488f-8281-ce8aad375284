package com.solum.xplain.xm.excmngmt.processipv;

import static org.springframework.util.CollectionUtils.isEmpty;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslator;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.workflow.value.WorkflowStatus;
import com.solum.xplain.xm.excmngmt.ExceptionManagementService;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidenceRepository;
import com.solum.xplain.xm.excmngmt.form.ApplyVerificationForm;
import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm;
import com.solum.xplain.xm.excmngmt.process.view.BreakCountView;
import com.solum.xplain.xm.excmngmt.processipv.form.ApplyResolutionForm;
import com.solum.xplain.xm.excmngmt.processipv.form.TradeFilterForm;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvExceptionManagementCountedFiltersView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView;
import com.solum.xplain.xm.excmngmt.processipv.view.chart.IpvTaskChartData;
import com.solum.xplain.xm.tasks.repository.IpvTaskDefinitionRepository;
import com.solum.xplain.xm.workflow.XmWorkflowService;
import com.solum.xplain.xm.workflow.form.ResolutionApprovalForm;
import com.solum.xplain.xm.workflow.form.VdResolutionDecisionForm;
import com.solum.xplain.xm.workflow.repository.XmStepInstanceQueryRepository;
import com.solum.xplain.xm.workflow.view.CallActivityProgressView;
import io.atlassian.fugue.Either;
import jakarta.annotation.Nullable;
import java.util.List;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class IpvExceptionManagementControllerService extends ExceptionManagementService {

  private final IpvExceptionManagementCalculationRepository repository;
  private final IpvExceptionManagementChartRepository chartRepository;
  private final IpvTaskDefinitionRepository taskDefinitionRepository;

  public IpvExceptionManagementControllerService(
      ExceptionManagementEvidenceRepository exceptionManagementEvidenceRepository,
      IpvExceptionManagementCalculationRepository repository,
      IpvExceptionManagementChartRepository chartRepository,
      IpvTaskDefinitionRepository taskDefinitionRepository,
      AuthenticationContext authenticationContext,
      ViewQueryTranslatorFactory viewQueryTranslatorFactory,
      XmStepInstanceQueryRepository xmStepInstanceQueryRepository,
      XmWorkflowService xmWorkflowService) {
    super(
        exceptionManagementEvidenceRepository,
        authenticationContext,
        viewQueryTranslatorFactory,
        xmStepInstanceQueryRepository,
        xmWorkflowService);
    this.repository = repository;
    this.chartRepository = chartRepository;
    this.taskDefinitionRepository = taskDefinitionRepository;
  }

  public ScrollableEntry<IpvTradeOverlayResultView> overlayItems(
      List<String> taskIds,
      ResultDisplayFilterForm displayFilter,
      TableFilter tableFilter,
      ScrollRequest scrollRequest) {
    ViewQueryTranslator viewQueryTranslator =
        viewQueryTranslatorFactory.getTranslator(IpvTradeOverlayResultView.class);
    return repository.overlayItems(
        taskIds,
        displayFilter,
        viewQueryTranslator.translate(tableFilter),
        viewQueryTranslator.translate(scrollRequest));
  }

  public CallActivityProgressView overlayProgress(String businessKey) {
    return xmStepInstanceQueryRepository.getVdEntryProgress(businessKey);
  }

  public BreakCountView overlayBreaksCount(List<String> taskIds) {
    return repository.overlayBreaksCount(taskIds);
  }

  public IpvExceptionManagementCountedFiltersView getTasksFilterForOverlay(
      List<String> taskIds, TradeFilterForm filter) {
    return repository.getOverlayTasksFilter(taskIds, filter);
  }

  public Either<ErrorItem, List<EntityId>> verifyResolutions(
      Authentication auth,
      ApplyVerificationForm form,
      MultipartFile evidence,
      TableFilter tableFilter) {
    return authenticationContext
        .userEither(auth)
        .flatMap(
            u -> {
              List<String> businessKeys = stepInstanceBusinessKeys(form, tableFilter, u);

              if (businessKeys.isEmpty()) {
                return Either.left(Error.OBJECT_NOT_FOUND.entity(NO_ACTIONABLE_ENTRIES_FOUND));
              }

              return storeWorkflowEvidence(businessKeys, evidence)
                  .map(
                      storedEvidence -> {
                        AuditUser auditUser = AuditUser.of(u);

                        xmWorkflowService.submitApprovalForm(
                            businessKeys,
                            new ResolutionApprovalForm(
                                form.overallVerification(), storedEvidence, auditUser));
                        return businessKeys.stream().map(EntityId::new).toList();
                      });
            });
  }

  private List<String> stepInstanceBusinessKeys(
      ApplyVerificationForm form, TableFilter tableFilter, XplainPrincipal user) {
    List<String> formKeys = null;

    if (!isEmpty(form.selectedIds())) {
      formKeys = form.selectedIds();
    }

    return stepInstanceBusinessKeys(
        formKeys, form.taskIds(), tableFilter, user, WorkflowStatus.ACTIVE);
  }

  private List<String> stepInstanceBusinessKeys(
      ApplyResolutionForm form, TableFilter tableFilter, XplainPrincipal user) {
    List<String> formKeys;

    if (!isEmpty(form.selectedIds())) {
      formKeys = form.selectedIds();
    } else {
      // Just get all the keys from the view for now
      var translator = viewQueryTranslatorFactory.getTranslator(IpvTradeOverlayResultView.class);
      var visible =
          repository.overlayItems(
              form.taskIds(),
              new ResultDisplayFilterForm(true, false, false),
              translator.translate(tableFilter),
              ScrollRequest.unconstrained());
      formKeys = visible.getContent().stream().map(IpvTradeOverlayResultView::getId).toList();
    }

    return stepInstanceBusinessKeys(
        formKeys, form.taskIds(), tableFilter, user, WorkflowStatus.ACTIVE);
  }

  private List<String> stepInstanceBusinessKeys(
      @Nullable List<String> formKeys,
      List<String> taskGroups,
      TableFilter tableFilter,
      XplainPrincipal user,
      WorkflowStatus status) {
    return stepInstanceBusinessKeys(formKeys, null, tableFilter, taskGroups, user, List.of(status));
  }

  private List<String> stepInstanceBusinessKeys(
      @Nullable List<String> formKeys,
      @Nullable ResultDisplayFilterForm displayFilter,
      @Nullable TableFilter tableFilter,
      List<String> taskGroups,
      XplainPrincipal user,
      List<WorkflowStatus> statuses) {
    final List<StepInstance> stepInstances;
    if (formKeys == null || formKeys.isEmpty()) {
      if (tableFilter != null) {
        var translator = viewQueryTranslatorFactory.getTranslator(IpvTradeOverlayResultView.class);
        var visible =
            repository.overlayItems(
                taskGroups,
                displayFilter == null
                    ? new ResultDisplayFilterForm(true, false, false)
                    : displayFilter,
                translator.translate(tableFilter),
                ScrollRequest.unconstrained());
        return visible.getContent().stream().map(IpvTradeOverlayResultView::getId).toList();
      } else {
        stepInstances =
            xmStepInstanceQueryRepository.findStepInstancesForVdUserTasks(
                taskGroups,
                statuses,
                user,
                taskDefinitionRepository.getTasksDefinitions(BitemporalDate.newOfNow()));
        return stepInstances.stream().map(StepInstance::getBusinessKey).toList();
      }
    }
    return xmWorkflowService
        .filterVdBusinessKeysByStepStatusAndUser(formKeys, statuses, user)
        .toList();
  }

  public Either<ErrorItem, List<EntityId>> applyResolution(
      Authentication auth,
      ApplyResolutionForm form,
      TableFilter tableFilter,
      MultipartFile evidence) {
    return authenticationContext
        .userEither(auth)
        .flatMap(
            u -> {
              List<String> businessKeys = stepInstanceBusinessKeys(form, tableFilter, u);

              if (businessKeys.isEmpty()) {
                return Either.left(Error.OBJECT_NOT_FOUND.entity(NO_ACTIONABLE_ENTRIES_FOUND));
              }

              return storeWorkflowEvidence(businessKeys, evidence)
                  .map(
                      storedEvidence -> {
                        AuditUser auditUser = AuditUser.of(u);
                        xmWorkflowService.submitResolutionForm(
                            businessKeys,
                            new VdResolutionDecisionForm(
                                form.overallResolution(), storedEvidence, auditUser));
                        return businessKeys.stream().map(EntityId::new).toList();
                      });
            });
  }

  public Either<ErrorItem, List<EntityId>> undoResolution(
      Authentication auth,
      List<String> taskIds,
      ResultDisplayFilterForm displayFilter,
      TableFilter tableFilter,
      List<String> resultsIds) {

    if (taskIds == null || taskIds.isEmpty()) {
      return Either.left(Error.OPERATION_NOT_ALLOWED.entity(NO_ACTIONABLE_ENTRIES_FOUND));
    }

    return authenticationContext
        .userEither(auth)
        .flatMap(
            u -> {
              List<String> businessKeys =
                  stepInstanceBusinessKeys(
                      resultsIds,
                      displayFilter,
                      tableFilter,
                      taskIds,
                      u,
                      List.of(WorkflowStatus.FINALIZING, WorkflowStatus.HELD));

              if (businessKeys.isEmpty()) {
                return Either.left(Error.OBJECT_NOT_FOUND.entity(NO_ACTIONABLE_ENTRIES_FOUND));
              }

              xmWorkflowService.clearVdResolution(businessKeys);
              return Either.right(businessKeys.stream().map(EntityId::new).toList());
            });
  }

  public Either<ErrorItem, List<EntityId>> undoApproval(
      Authentication auth,
      List<String> taskIds,
      ResultDisplayFilterForm displayFilter,
      TableFilter tableFilter,
      List<String> resultsIds) {

    if (taskIds == null || taskIds.isEmpty()) {
      return Either.left(Error.OPERATION_NOT_ALLOWED.entity(NO_ACTIONABLE_ENTRIES_FOUND));
    }

    return authenticationContext
        .userEither(auth)
        .flatMap(
            u -> {
              List<String> businessKeys =
                  stepInstanceBusinessKeys(
                      resultsIds,
                      displayFilter,
                      tableFilter,
                      taskIds,
                      u,
                      List.of(WorkflowStatus.FINALIZING, WorkflowStatus.HELD));

              if (businessKeys.isEmpty()) {
                return Either.left(Error.OBJECT_NOT_FOUND.entity(NO_ACTIONABLE_ENTRIES_FOUND));
              }

              xmWorkflowService.clearVdApproval(businessKeys);
              return Either.right(businessKeys.stream().map(EntityId::new).toList());
            });
  }

  public IpvTaskChartData taskCharts(List<String> taskIds) {
    return chartRepository.chartData(taskIds);
  }
}
