package com.solum.xplain.xm.excmngmt.process;

import static com.solum.xplain.core.common.AuditContext.contextNow;
import static com.solum.xplain.core.common.CollectionUtils.convertCollectionTo;
import static org.springframework.util.CollectionUtils.isEmpty;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslator;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory;
import com.solum.xplain.shared.utils.filter.TableFilter;
import com.solum.xplain.workflow.entity.StepInstance;
import com.solum.xplain.workflow.value.WorkflowStatus;
import com.solum.xplain.xm.excmngmt.ExceptionManagementService;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidenceRepository;
import com.solum.xplain.xm.excmngmt.form.ApplyVerificationForm;
import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm;
import com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus;
import com.solum.xplain.xm.excmngmt.process.form.ApplyTaskResolutionForm;
import com.solum.xplain.xm.excmngmt.process.form.InstrumentFilterForm;
import com.solum.xplain.xm.excmngmt.process.form.InstrumentResultBreaksFilterForm;
import com.solum.xplain.xm.excmngmt.process.view.BreakCountView;
import com.solum.xplain.xm.excmngmt.process.view.ExceptionManagementCountedFiltersView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView;
import com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView;
import com.solum.xplain.xm.excmngmt.process.view.chart.TaskChartData;
import com.solum.xplain.xm.tasks.entity.TaskExecution;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus;
import com.solum.xplain.xm.tasks.repository.TaskDefinitionRepository;
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository;
import com.solum.xplain.xm.workflow.XmWorkflowService;
import com.solum.xplain.xm.workflow.form.MdResolutionDecisionForm;
import com.solum.xplain.xm.workflow.form.ResolutionApprovalForm;
import com.solum.xplain.xm.workflow.repository.XmStepInstanceQueryRepository;
import com.solum.xplain.xm.workflow.view.CallActivityProgressView;
import io.atlassian.fugue.Either;
import jakarta.annotation.Nullable;
import java.util.List;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class ExceptionManagementControllerService extends ExceptionManagementService {

  private final ExceptionManagementCalculationRepository calculationRepository;
  private final TaskExecutionRepository taskExecutionRepository;
  private final TaskDefinitionRepository taskDefinitionRepository;
  private final ExceptionManagementChartRepository chartRepository;

  public ExceptionManagementControllerService(
      ExceptionManagementEvidenceRepository exceptionManagementEvidenceRepository,
      ExceptionManagementCalculationRepository calculationRepository,
      TaskExecutionRepository taskExecutionRepository,
      TaskDefinitionRepository taskDefinitionRepository,
      AuthenticationContext authenticationContext,
      ViewQueryTranslatorFactory viewQueryTranslatorFactory,
      XmStepInstanceQueryRepository xmStepInstanceQueryRepository,
      XmWorkflowService xmWorkflowService,
      ExceptionManagementChartRepository chartRepository) {
    super(
        exceptionManagementEvidenceRepository,
        authenticationContext,
        viewQueryTranslatorFactory,
        xmStepInstanceQueryRepository,
        xmWorkflowService);
    this.calculationRepository = calculationRepository;
    this.taskExecutionRepository = taskExecutionRepository;
    this.taskDefinitionRepository = taskDefinitionRepository;
    this.chartRepository = chartRepository;
  }

  public ScrollableEntry<InstrumentPreliminaryResultView> preliminaryItems(
      List<String> taskIds,
      ResultDisplayFilterForm displayFilter,
      TableFilter tableFilter,
      ScrollRequest scrollRequest) {
    ViewQueryTranslator viewQueryTranslator =
        viewQueryTranslatorFactory.getTranslator(InstrumentPreliminaryResultView.class);
    return calculationRepository.preliminaryItemsDaily(
        displayFilter,
        taskIds,
        viewQueryTranslator.translate(tableFilter),
        viewQueryTranslator.translate(scrollRequest));
  }

  public ScrollableEntry<InstrumentOverlayResultView> overlayItems(
      List<String> taskIds,
      ResultDisplayFilterForm displayFilter,
      TableFilter tableFilter,
      ScrollRequest scrollRequest) {
    ViewQueryTranslator viewQueryTranslator =
        viewQueryTranslatorFactory.getTranslator(InstrumentOverlayResultView.class);

    return calculationRepository.overlayItems(
        taskIds,
        displayFilter,
        viewQueryTranslator.translate(tableFilter),
        viewQueryTranslator.translate(scrollRequest));
  }

  public Either<ErrorItem, List<EntityId>> applyResolution(
      Authentication auth,
      ApplyTaskResolutionForm form,
      TableFilter tableFilter,
      MultipartFile evidence,
      CalculationTestStatus status) {
    return authenticationContext
        .userEither(auth)
        .flatMap(
            u -> {
              List<String> businessKeys =
                  stepInstanceBusinessKeys(
                      form, tableFilter, u, status.getTaskExceptionManagementType());

              if (businessKeys.isEmpty()) {
                // TODO: SXSD-9015: legacy market data dashboard - replace with error similar to
                //  IpvExceptionManagementControllerService.applyResolution
                return validate(form.getTaskIds(), TaskExecutionStatus.IN_RESOLUTION)
                    .flatMap(tasks -> storeEvidence(tasks, evidence))
                    .flatMap(
                        storedEvidence ->
                            calculationRepository.saveResult(
                                contextNow(u),
                                form,
                                status,
                                EntryResultStatus.WAITING_APPROVAL,
                                storedEvidence));
              }

              return storeWorkflowEvidence(businessKeys, evidence)
                  .map(
                      storedEvidence -> {
                        AuditUser auditUser = AuditUser.of(u);
                        submitMdResolutionForm(
                            businessKeys,
                            new MdResolutionDecisionForm(
                                form.getOverallResolution(), storedEvidence, auditUser),
                            status.getTaskExceptionManagementType());
                        return businessKeys.stream().map(EntityId::new).toList();
                      });
            });
  }

  private void submitMdResolutionForm(
      List<String> businessKeys, MdResolutionDecisionForm form, TaskExceptionManagementType type) {
    switch (type) {
      case PRELIMINARY -> xmWorkflowService.submitMdPreliminaryResolutionForm(businessKeys, form);
      case OVERLAY -> xmWorkflowService.submitMdOverlayResolutionForm(businessKeys, form);
      case PRELIMINARY_BATCH ->
          throw new UnsupportedOperationException(
              "Batch workflow not yet supported"); // TODO SXSD-9013 Batch workflow
    }
  }

  public CallActivityProgressView progress(String businessKey) {
    return xmStepInstanceQueryRepository.getMdEntryProgress(businessKey);
  }

  public Either<ErrorItem, List<EntityId>> undoResolution(
      Authentication auth,
      List<String> taskIds,
      List<String> resultIds,
      TableFilter tableFilter,
      CalculationTestStatus status) {

    if (taskIds == null || taskIds.isEmpty()) {
      return Either.left(
          com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED.entity(
              NO_ACTIONABLE_ENTRIES_FOUND));
    }

    return authenticationContext
        .userEither(auth)
        .flatMap(
            u -> {
              List<String> businessKeys =
                  stepInstanceBusinessKeys(
                      resultIds,
                      tableFilter,
                      taskIds,
                      u,
                      List.of(WorkflowStatus.FINALIZING, WorkflowStatus.HELD),
                      status.getTaskExceptionManagementType());

              if (businessKeys.isEmpty()) {
                // TODO: SXSD-9015: legacy market data dashboard - replace with error similar to
                // IpvExceptionManagementControllerService.undoResolution
                return validate(taskIds, TaskExecutionStatus.IN_RESOLUTION)
                    .map(tasks -> convertCollectionTo(tasks, TaskExecution::getId))
                    .flatMap(ids -> calculationRepository.undoResolution(resultIds, ids, status));
              }
              switch (status.getTaskExceptionManagementType()) {
                case PRELIMINARY -> xmWorkflowService.clearMdPreliminaryResolution(businessKeys);
                case OVERLAY -> xmWorkflowService.clearMdOverlayResolution(businessKeys);
                case PRELIMINARY_BATCH ->
                    throw new UnsupportedOperationException(
                        "Batch workflow not yet supported"); // TODO SXSD-9013 Batch workflow
              }
              return Either.right(businessKeys.stream().map(EntityId::new).toList());
            });
  }

  public Either<ErrorItem, List<EntityId>> undoApproval(
      Authentication auth,
      List<String> taskIds,
      List<String> resultIds,
      TableFilter tableFilter,
      CalculationTestStatus status) {

    if (taskIds == null || taskIds.isEmpty()) {
      return Either.left(
          com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED.entity(
              NO_ACTIONABLE_ENTRIES_FOUND));
    }

    return authenticationContext
        .userEither(auth)
        .flatMap(
            u -> {
              List<String> businessKeys =
                  stepInstanceBusinessKeys(
                      resultIds,
                      tableFilter,
                      taskIds,
                      u,
                      List.of(WorkflowStatus.FINALIZING, WorkflowStatus.HELD),
                      status.getTaskExceptionManagementType());

              if (businessKeys.isEmpty()) {
                return validate(taskIds, TaskExecutionStatus.IN_APPROVAL)
                    .map(tasks -> convertCollectionTo(tasks, TaskExecution::getId))
                    .flatMap(ids -> calculationRepository.undoResolution(resultIds, ids, status));
              }
              switch (status.getTaskExceptionManagementType()) {
                case PRELIMINARY -> xmWorkflowService.clearMdPreliminaryApproval(businessKeys);
                case OVERLAY -> xmWorkflowService.clearMdOverlayApproval(businessKeys);
                case PRELIMINARY_BATCH ->
                    throw new UnsupportedOperationException(
                        "Batch workflow not yet supported"); // TODO SXSD-9013 Batch workflow
              }
              return Either.right(businessKeys.stream().map(EntityId::new).toList());
            });
  }

  public Either<ErrorItem, List<EntityId>> verifyResolutions(
      Authentication auth,
      ApplyVerificationForm form,
      MultipartFile evidence,
      CalculationTestStatus status,
      TableFilter tableFilter) {
    return authenticationContext
        .userEither(auth)
        .flatMap(
            u -> {
              List<String> businessKeys =
                  stepInstanceBusinessKeys(
                      form, tableFilter, u, status.getTaskExceptionManagementType());

              if (businessKeys.isEmpty()) {
                // TODO: SXSD-9015: legacy market data dashboard - replace with error similar to
                //  IpvExceptionManagementControllerService.verifyResolutions
                return validate(form.taskIds(), TaskExecutionStatus.IN_APPROVAL)
                    .flatMap(tasks -> storeEvidence(tasks, evidence))
                    .flatMap(
                        storedEvidence ->
                            calculationRepository.verifyItems(form, status, storedEvidence));
              }

              return storeWorkflowEvidence(businessKeys, evidence)
                  .map(
                      storedEvidence -> {
                        AuditUser auditUser = AuditUser.of(u);
                        submitMdApprovalForm(
                            businessKeys,
                            new ResolutionApprovalForm(
                                form.overallVerification(), storedEvidence, auditUser),
                            status.getTaskExceptionManagementType());
                        return businessKeys.stream().map(EntityId::new).toList();
                      });
            });
  }

  private void submitMdApprovalForm(
      List<String> businessKeys, ResolutionApprovalForm form, TaskExceptionManagementType type) {
    switch (type) {
      case PRELIMINARY -> xmWorkflowService.submitMdPreliminaryApprovalForm(businessKeys, form);
      case OVERLAY -> xmWorkflowService.submitMdOverlayApprovalForm(businessKeys, form);
      case PRELIMINARY_BATCH ->
          throw new UnsupportedOperationException(
              "Batch workflow not yet supported"); // TODO SXSD-9013 Batch workflow
    }
  }

  public ExceptionManagementCountedFiltersView getTasksFilterForPreliminary(
      List<String> taskIds, InstrumentFilterForm filter) {
    return calculationRepository.getPreliminaryTasksFilter(taskIds, filter);
  }

  public ExceptionManagementCountedFiltersView getTasksFilterForOverlay(
      List<String> taskIds, InstrumentFilterForm filter) {
    return calculationRepository.getOverlayTasksFilter(taskIds, filter);
  }

  public BreakCountView preliminaryBreaksCount(
      InstrumentResultBreaksFilterForm filter, List<String> taskIds) {
    return calculationRepository.preliminaryBreakCount(filter, taskIds);
  }

  public BreakCountView overlayBreaksCount(
      InstrumentResultBreaksFilterForm filter, List<String> taskIds) {
    return calculationRepository.overlayBreaksCount(filter, taskIds);
  }

  private Either<ErrorItem, List<TaskExecution>> validate(
      List<String> taskIds, TaskExecutionStatus status) {
    return validateTasks(taskExecutionRepository.executions(taskIds), status);
  }

  private List<String> stepInstanceBusinessKeys(
      ApplyTaskResolutionForm form,
      TableFilter tableFilter,
      XplainPrincipal user,
      TaskExceptionManagementType type) {
    List<String> formKeys;

    if (!isEmpty(form.getSelectedIds())) {
      formKeys = form.getSelectedIds();
    } else {
      formKeys = fetchBusinessKeysForType(form.getTaskIds(), tableFilter, type);
    }
    return stepInstanceBusinessKeys(
        formKeys, form.getTaskIds(), user, WorkflowStatus.ACTIVE, tableFilter, type);
  }

  private List<String> stepInstanceBusinessKeys(
      ApplyVerificationForm form,
      TableFilter tableFilter,
      XplainPrincipal user,
      TaskExceptionManagementType type) {
    List<String> formKeys = null;

    if (!isEmpty(form.selectedIds())) {
      formKeys = form.selectedIds();
    }

    return stepInstanceBusinessKeys(
        formKeys, form.taskIds(), user, WorkflowStatus.ACTIVE, tableFilter, type);
  }

  protected List<String> stepInstanceBusinessKeys(
      @Nullable List<String> formKeys,
      List<String> taskGroups,
      XplainPrincipal user,
      WorkflowStatus status,
      TableFilter tableFilter,
      TaskExceptionManagementType type) {
    return stepInstanceBusinessKeys(formKeys, tableFilter, taskGroups, user, List.of(status), type);
  }

  protected List<String> stepInstanceBusinessKeys(
      @Nullable List<String> formKeys,
      @Nullable TableFilter tableFilter,
      List<String> taskGroups,
      XplainPrincipal user,
      List<WorkflowStatus> statuses,
      TaskExceptionManagementType type) {
    final List<StepInstance> stepInstances;

    if (formKeys == null || formKeys.isEmpty()) {
      if (tableFilter != null) {
        formKeys = fetchBusinessKeysForType(taskGroups, tableFilter, type);
      } else {
        try {
          stepInstances =
              xmStepInstanceQueryRepository.findStepInstancesForMdUserTasks(
                  taskGroups,
                  statuses,
                  user,
                  taskDefinitionRepository.getTasksDefinitions(BitemporalDate.newOfNow()));
          return stepInstances.stream().map(StepInstance::getBusinessKey).toList();

        } catch (IllegalArgumentException e) {
          return List.of();
        }
      }
    }

    return (switch (type) {
          case PRELIMINARY ->
              xmWorkflowService.filterMdPreliminaryBusinessKeysByStepStatusAndUser(
                  formKeys, statuses, user);
          case OVERLAY ->
              xmWorkflowService.filterMdOverlayBusinessKeysByStepStatusAndUser(
                  formKeys, statuses, user);
          case PRELIMINARY_BATCH ->
              throw new UnsupportedOperationException(
                  "Batch workflow not yet supported"); // TODO SXSD-9013 Batch workflow
        })
        .toList();
  }

  private List<String> fetchBusinessKeysForType(
      List<String> taskIds, TableFilter tableFilter, TaskExceptionManagementType type) {

    switch (type) {
      case PRELIMINARY -> {
        var translator =
            viewQueryTranslatorFactory.getTranslator(InstrumentPreliminaryResultView.class);
        var visible =
            calculationRepository.preliminaryItemsDaily(
                new ResultDisplayFilterForm(true, false, false),
                taskIds,
                translator.translate(tableFilter),
                ScrollRequest.unconstrained());
        return visible.getContent().stream().map(InstrumentPreliminaryResultView::getId).toList();
      }
      case OVERLAY -> {
        var translator =
            viewQueryTranslatorFactory.getTranslator(InstrumentOverlayResultView.class);
        var visible =
            calculationRepository.overlayItems(
                taskIds,
                new ResultDisplayFilterForm(true, false, false),
                translator.translate(tableFilter),
                ScrollRequest.unconstrained());
        return visible.getContent().stream().map(InstrumentOverlayResultView::getId).toList();
      }
      case PRELIMINARY_BATCH ->
          throw new UnsupportedOperationException("Batch workflow not yet supported");
      default -> throw new IllegalArgumentException("Unsupported type: " + type);
    }
  }

  public TaskChartData preliminaryTaskCharts(List<String> taskIds) {
    return chartRepository.preliminaryChartData(taskIds);
  }

  public TaskChartData overlayTaskCharts(List<String> taskIds) {
    return chartRepository.overlayChartData(taskIds);
  }
}
