package com.solum.xplain.xm.excmngmt.processipv.value;

import static java.util.stream.Collectors.toMap;

import com.solum.xplain.core.common.versions.MinorVersionedEntity;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak;
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.NonNull;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@FieldNameConstants
public class IpvBreakTestCalculations implements Serializable {

  private final List<IpvBreakTestCalculation> calculations;

  public static IpvBreakTestCalculations ofBreakTests(@NonNull List<IpvBreakTest> breakTests) {
    var rootTests =
        breakTests.stream()
            .filter(r -> r.getParentTest() == null)
            .collect(
                toMap(
                    MinorVersionedEntity::getEntityId,
                    IpvBreakTestCalculation::ofBreakTest,
                    (a, b) -> a,
                    LinkedHashMap::new));

    breakTests.stream()
        .filter(r -> r.getParentTest() != null)
        .forEach(r -> rootTests.get(r.getParentTest().getParentId()).acceptDependant(r));

    return new IpvBreakTestCalculations(List.copyOf(rootTests.values()));
  }

  public Optional<Integer> longestStaleDuration() {
    return calculations.stream()
        .map(IpvBreakTestCalculation::staleValueDuration)
        .flatMap(Optional::stream)
        .max(Integer::compareTo);
  }

  public IpvBreakTestCalculations filteredFor(Trade trade) {
    return new IpvBreakTestCalculations(
        calculations.stream()
            .filter(ipvBreakTestCalculation -> ipvBreakTestCalculation.isApplicable(trade))
            .toList());
  }

  /**
   * The break test calculations are empty if there are no {@linkplain
   * IpvBreakTestCalculation#isRelevant() relevant} calculations.
   *
   * @return true if there are no relevant calculations
   */
  public boolean isEmpty() {
    return calculations.stream().noneMatch(IpvBreakTestCalculation::isRelevant);
  }

  public List<TradeResultBreak> processCalc(
      TradeBreakCalculator tradeCalculator, LocalDate stateDate) {
    return calculations.stream()
        .map(b -> b.resolveBreak(tradeCalculator, stateDate))
        .flatMap(Collection::stream)
        .sorted(TradeResultBreak.comparator())
        .toList();
  }
}
