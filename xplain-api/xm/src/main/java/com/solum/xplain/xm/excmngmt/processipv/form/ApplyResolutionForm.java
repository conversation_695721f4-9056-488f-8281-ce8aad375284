package com.solum.xplain.xm.excmngmt.processipv.form;

import com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType;
import com.solum.xplain.xm.excmngmt.processipv.form.validation.ValidIpvResolutionTypes;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;
import org.hibernate.validator.group.GroupSequenceProvider;

@GroupSequenceProvider(ApplyResolutionFormGroupProvider.class)
@ValidIpvResolutionTypes
public record ApplyResolutionForm(
    @NotEmpty List<String> taskIds,
    @NotNull @Valid ResolutionForm overallResolution,
    @NotNull List<String> selectedIds) {

  public Set<TradeResultResolutionType> resolutionTypes() {
    if (overallResolution != null) {
      return overallResolution.resolutionType() == null
          ? Set.of()
          : Set.of(overallResolution.resolutionType());
    }
    return Set.of();
  }
}
