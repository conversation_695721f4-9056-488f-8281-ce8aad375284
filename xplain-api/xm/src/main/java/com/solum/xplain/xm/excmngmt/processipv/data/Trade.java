package com.solum.xplain.xm.excmngmt.processipv.data;

import com.solum.xplain.core.company.mapper.TradeFact;
import com.solum.xplain.core.portfolio.trade.CustomTradeField;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductGroup;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.extensions.enums.CreditSector;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * XM: Trade represents something we are exception managing valuation of. This relates to a {@link
 * com.solum.xplain.core.portfolio.PortfolioItem} via {@link
 * com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementResultMapper}.
 */
@Data
@FieldNameConstants
public class Trade implements TradeFact, Serializable {

  private String key;
  private String currency;
  private String currencyPair;
  private String underlying;
  private ProductType productType;
  private ProductGroup productGroup;
  private String externalTradeId;
  private List<ExternalIdentifier> externalIdentifiers;
  private List<CustomTradeField> customFields;
  private TradeDetails tradeDetails;
  private Double notional;
  private Double dealCost;
  private Double accountingCost;
  private LocalDate vendorOnboardingDate;
  private LocalDate onboardingPeriodEndDate;
  private Boolean vendorCheck;
  private LocalDate validFrom;
  private LocalDateTime recordFrom;

  private String portfolioId;
  private String portfolioExternalId;
  private String externalCompanyId;
  private String externalEntityId;

  /** The entity id of the trade versioned entity. */
  private String entityId;

  private CreditSector creditSector;
}
