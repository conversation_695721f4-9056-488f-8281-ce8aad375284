package com.solum.xplain.xm.excmngmt.processipv;

import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.ipv.ValuationDataKeyUtils;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.product.details.TradeDetailsResolver;
import com.solum.xplain.xm.excmngmt.CountedFilterMapper;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.value.IpvExceptionManagementCountedFilters;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvExceptionManagementCountedFiltersView;
import org.mapstruct.Mapper;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(uses = {ObjectIdMapper.class, CountedFilterMapper.class})
public abstract class IpvExceptionManagementResultMapper {

  @Autowired private TradeDetailsResolver detailsResolver;

  public Trade fromPortfolioItem(PortfolioItem item) {
    Trade trade = new Trade();
    trade.setPortfolioExternalId(item.getExternalPortfolioId());
    trade.setPortfolioId(item.getPortfolioId().toHexString());
    trade.setExternalCompanyId(item.getExternalCompanyId());
    trade.setExternalEntityId(item.getExternalEntityId());
    trade.setEntityId(item.getEntityId());
    trade.setExternalTradeId(item.getExternalTradeId());
    trade.setExternalIdentifiers(item.getExternalIdentifiers());
    trade.setCustomFields(item.getCustomFields());
    trade.setProductType(item.getProductType());
    trade.setProductGroup(item.getProductType().getGroup());
    trade.setTradeDetails(item.getTradeDetails());
    trade.setCurrency(item.getTradeDetails().tradeCurrency().getCode());
    var currencyPair = item.getTradeDetails().currencyPair();
    trade.setCurrencyPair(currencyPair == null ? null : currencyPair.toString());
    trade.setCreditSector(
        item.getTradeDetails().getCreditTradeDetails() == null
            ? null
            : item.getTradeDetails().getCreditTradeDetails().getSector());
    trade.setKey(
        ValuationDataKeyUtils.toValuationDataKey(
            item.getExternalCompanyId(),
            item.getExternalEntityId(),
            item.getExternalPortfolioId(),
            item.getExternalTradeId()));
    trade.setNotional(
        detailsResolver.resolveNotional(item.getProductType(), item.getTradeDetails()));
    trade.setUnderlying(
        detailsResolver.resolveUnderlying(item.getProductType(), item.getTradeDetails()));
    trade.setDealCost(
        item.getOnboardingDetails() == null ? null : item.getOnboardingDetails().getDealCost());
    trade.setAccountingCost(
        item.getOnboardingDetails() == null
            ? null
            : item.getOnboardingDetails().getAccountingCost());
    trade.setVendorOnboardingDate(
        item.getOnboardingDetails() == null
            ? null
            : item.getOnboardingDetails().getVendorOnboardingDate());
    trade.setOnboardingPeriodEndDate(
        item.getOnboardingDetails() == null
            ? null
            : item.getOnboardingDetails().getOnboardingPeriodEndDate());
    trade.setVendorCheck(
        item.getOnboardingDetails() == null ? null : item.getOnboardingDetails().getVendorCheck());
    trade.setValidFrom(item.getValidFrom());
    trade.setRecordFrom(item.getRecordFrom());
    return trade;
  }

  abstract IpvExceptionManagementCountedFiltersView toView(
      IpvExceptionManagementCountedFilters filters);
}
