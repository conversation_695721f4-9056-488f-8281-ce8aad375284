package com.solum.xplain.xm.excmngmt.processipv.form;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springdoc.core.annotations.ParameterObject;

@Data
@ParameterObject
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class TradeResultTasksBreaksFilterForm extends TradeResultBreaksFilterForm {

  @NotNull private List<String> taskIds;

  public TradeResultTasksBreaksFilterForm(
      List<String> taskIds,
      Boolean onlyCurvesWithBreaks,
      Boolean includeHeld,
      TradeFilterForm filter,
      Boolean onboardingOnly) {
    super(onlyCurvesWithBreaks, includeHeld, filter, onboardingOnly);
    this.taskIds = taskIds;
  }
}
