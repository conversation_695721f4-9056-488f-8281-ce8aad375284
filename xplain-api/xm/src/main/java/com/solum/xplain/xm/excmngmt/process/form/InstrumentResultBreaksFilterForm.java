package com.solum.xplain.xm.excmngmt.process.form;

import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springdoc.core.annotations.ParameterObject;

@Data
@ParameterObject
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class InstrumentResultBreaksFilterForm extends ResultDisplayFilterForm {

  @NotNull @Valid private InstrumentFilterForm filter;

  public InstrumentResultBreaksFilterForm(
      @NotNull Boolean onlyCurvesWithBreaks, InstrumentFilterForm filter) {
    super(onlyCurvesWithBreaks, false, false);
    this.filter = filter;
  }
}
