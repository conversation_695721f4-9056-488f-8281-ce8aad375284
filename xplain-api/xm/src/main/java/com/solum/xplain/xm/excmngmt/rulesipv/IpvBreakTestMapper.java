package com.solum.xplain.xm.excmngmt.rulesipv;

import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.rules.BreakTestParent;
import com.solum.xplain.xm.excmngmt.rulesbase.BaseBreakTestMapper;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestForm;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestOverrideForm;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestOverrideView;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvBreakTestView;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    uses = {ObjectIdMapper.class, TradeFilterMapper.class},
    imports = {IpvTestType.class})
public interface IpvBreakTestMapper
    extends BaseBreakTestMapper<
        Trade,
        IpvBreakTestOverride,
        IpvBreakTest,
        IpvBreakTestForm,
        IpvBreakTestOverrideForm,
        IpvBreakTestView,
        IpvBreakTestOverrideView> {

  @Override
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "sequence", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "archived", ignore = true)
  @Mapping(target = "overrides", ignore = true)
  @Mapping(target = "name", source = "form.name")
  @Mapping(target = "parentTest", source = "parentTest")
  @Mapping(
      target = "onboardingTest",
      expression = "java(form.getType() == IpvTestType.PRIMARY_VS_ACCOUNTING_COST)")
  IpvBreakTest fromForm(
      IpvBreakTestForm form, BreakTestParent parentTest, @MappingTarget IpvBreakTest v);
}
