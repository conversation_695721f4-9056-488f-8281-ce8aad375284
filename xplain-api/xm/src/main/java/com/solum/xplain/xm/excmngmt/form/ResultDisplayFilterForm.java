package com.solum.xplain.xm.excmngmt.form;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;

@Data
@ParameterObject
@AllArgsConstructor
@NoArgsConstructor
public class ResultDisplayFilterForm {

  @NotNull private Boolean onlyCurvesWithBreaks;

  /**
   * Set to true to include entries which are on hold in the results. Default is not to include
   * them.
   */
  @NotNull private Boolean includeHeld;

  /** Set to true to only include entries which triggered onboarding break tests. */
  @NotNull private Boolean onboardingOnly;
}
