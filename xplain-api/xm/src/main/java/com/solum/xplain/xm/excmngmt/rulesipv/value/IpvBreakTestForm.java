package com.solum.xplain.xm.excmngmt.rulesipv.value;

import com.solum.xplain.core.common.validation.ValidObjectId;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.processipv.validation.ValidXmClassifierStringSet;
import com.solum.xplain.xm.excmngmt.rules.value.Operator;
import com.solum.xplain.xm.excmngmt.rulesbase.value.BaseBreakTestForm;
import com.solum.xplain.xm.excmngmt.rulesipv.validation.ValidIpvBreakTestForm;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Jacksonized
@SuperBuilder
@ValidIpvBreakTestForm
@GroupSequenceProvider(IpvBreakTestGroupProvider.class)
public class IpvBreakTestForm extends BaseBreakTestForm {

  @NotNull private final IpvExceptionManagementPhase scope;

  @NotNull private final IpvTestType type;

  @NotNull(
      groups = {
        DayToDayGroup.class,
        ProviderToProviderGroup.class,
        ValueTestGroup.class,
        PrimaryToAccountingCostTestGroup.class
      })
  @Null(
      groups = {
        NullTestGroup.class,
        ZeroTestGroup.class,
        StaleTestGroup.class,
        DayToDaySignTestGroup.class
      })
  private final IpvMeasureType measureType;

  @ValidObjectId
  @Null(
      groups = {
        NullTestGroup.class,
        ZeroTestGroup.class,
        StaleTestGroup.class,
        DayToDayGroup.class,
        ValueTestGroup.class,
        DayToDaySignTestGroup.class
      })
  private final String parentBreakTestId;

  @NotNull(
      groups = {
        DayToDayGroup.class,
        ValueTestGroup.class,
        DayToDaySignTestGroup.class,
        ZeroTestGroup.class
      })
  @Null(
      groups = {
        ProviderToProviderGroup.class,
        StaleTestGroup.class,
        PrimaryToAccountingCostTestGroup.class
      })
  private final List<IpvProvidersType> providersTypes;

  @NotNull private final ProductTypeFilterForm tradeFilter;

  @NotNull(
      groups = {
        DayToDayGroup.class,
        ProviderToProviderGroup.class,
        ValueTestGroup.class,
        PrimaryToAccountingCostTestGroup.class
      })
  @Null(
      groups = {
        NullTestGroup.class,
        ZeroTestGroup.class,
        StaleTestGroup.class,
        DayToDaySignTestGroup.class
      })
  @ValidXmClassifierStringSet(classifier = "breakTestOperators")
  private final Operator operator;

  @NotEmpty(
      groups = {
        DayToDayGroup.class,
        ProviderToProviderGroup.class,
        ValueTestGroup.class,
        StaleTestGroup.class,
        PrimaryToAccountingCostTestGroup.class
      })
  @Null(groups = {NullTestGroup.class, ZeroTestGroup.class, DayToDaySignTestGroup.class})
  private final List<BigDecimal> threshold;
}
