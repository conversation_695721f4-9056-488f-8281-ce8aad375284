//file:noinspection UnnecessaryQualifiedReference
package com.solum.xplain.xm.workflow

import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.core.users.UserBuilder.userWithTeams
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.APPROVE_RESOLUTION_TASK
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.DECIDE_RESOLUTION_TASK
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.VD_XM_CALC_PROCESS_ID
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID
import static org.springframework.data.mongodb.core.query.Criteria.where

import com.solum.xplain.calculation.events.TradesCalculationFinishedEvent
import com.solum.xplain.core.calculationapi.CalculationEventProducer
import com.solum.xplain.core.calculationapi.CalculationResultProvider
import com.solum.xplain.core.calculationapi.DashboardCalculation
import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.value.NewMinorVersionForm
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.entity.CompanyIpvSettings
import com.solum.xplain.core.company.entity.CompanyLegalEntity
import com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings
import com.solum.xplain.core.company.entity.CompanyValuationSettings
import com.solum.xplain.core.company.events.CompanyCreated
import com.solum.xplain.core.company.events.CompanyLegalEntityCreated
import com.solum.xplain.core.company.form.IpvValuationProvidersForm
import com.solum.xplain.core.company.form.IpvValuationSettingsForm
import com.solum.xplain.core.company.form.ValuationDataProvidersForm
import com.solum.xplain.core.company.repository.CompanyIpvSettingsRepository
import com.solum.xplain.core.company.value.CompanyLegalEntityValuationSettingsView
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRates
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.ipv.group.entity.IpvDataGroup
import com.solum.xplain.core.ipv.tradeleveloverride.repository.TradeLevelOverrideWriteRepository
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideForm
import com.solum.xplain.core.market.MarketDataGroup
import com.solum.xplain.core.market.MarketDataKey
import com.solum.xplain.core.mdvalue.entity.MarketDataValue
import com.solum.xplain.core.portfolio.CalculationStatus
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.Portfolio
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.PortfolioItemEntity
import com.solum.xplain.core.portfolio.repository.PortfolioItemWriteRepository
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import com.solum.xplain.core.portfolio.value.PortfolioItemCalculatedExcMngmntView
import com.solum.xplain.core.providers.DataProvider
import com.solum.xplain.core.settings.entity.GlobalValuationSettings
import com.solum.xplain.core.settings.entity.IpvTaskDefaultTeams
import com.solum.xplain.core.settings.repository.GlobalValuationSettingsRepository
import com.solum.xplain.core.teams.Team
import com.solum.xplain.core.teams.TeamBuilder
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.utils.PathUtils
import com.solum.xplain.data.valuation.ipv.IpvDataRepository
import com.solum.xplain.data.valuation.ipv.entity.IpvDataValue
import com.solum.xplain.data.valuation.ipv.form.IpvDataProviderValueUpdateForm
import com.solum.xplain.generic.type.details.NonFxGenericProductDetailsResolver
import com.solum.xplain.generic.type.product.GenericProductGroup
import com.solum.xplain.generic.type.product.GenericProductType
import com.solum.xplain.generic.type.product.GenericProductTypeProvider
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.shared.utils.spock.LogSteps
import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.repository.DataModificationCommandQueue
import com.solum.xplain.workflow.service.WorkflowService
import com.solum.xplain.workflow.service.engine.WorkflowEngine
import com.solum.xplain.workflow.value.WorkflowStatus
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.entity.VdExceptionManagementSetup
import com.solum.xplain.xm.dashboards.enums.DashboardStep
import com.solum.xplain.xm.dashboards.enums.StepStatus
import com.solum.xplain.xm.dashboards.events.DashboardDeletedEvent
import com.solum.xplain.xm.dashboards.forms.PricingSlotPortfolioForm
import com.solum.xplain.xm.dashboards.resolver.VdExceptionManagementPortfolioFilterBuilder
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.excmngmt.enums.VerificationStatus
import com.solum.xplain.xm.excmngmt.form.ApplyVerificationForm
import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm
import com.solum.xplain.xm.excmngmt.form.VerificationForm
import com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementCalculationRepository
import com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementControllerService
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay
import com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType
import com.solum.xplain.xm.excmngmt.processipv.form.ApplyResolutionForm
import com.solum.xplain.xm.excmngmt.processipv.form.ResolutionForm
import com.solum.xplain.xm.excmngmt.processipv.resolution.data.TradeResultResolutionSubTypeReference
import com.solum.xplain.xm.excmngmt.rules.value.Operator
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest
import com.solum.xplain.xm.excmngmt.rulesipv.TradeFilter
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvMeasureType
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvTestType
import com.solum.xplain.xm.settings.ExceptionManagementSettings
import com.solum.xplain.xm.settings.IpvValueCurrencyType
import com.solum.xplain.xm.tasks.entity.IpvTasksDefinition
import com.solum.xplain.xm.tasks.enums.IpvTaskExecutionRerunType
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus
import com.solum.xplain.xm.tasks.form.IpvTaskExecutionRerunRequest
import com.solum.xplain.xm.tasks.service.IpvTaskExecutionService
import com.solum.xplain.xm.tasks.service.IpvTaskReRunExecutionService
import com.solum.xplain.xm.workflow.repository.XmIpvTasksDefinitionQueryRepository
import com.solum.xplain.xm.workflow.state.VdPhaseContext
import com.solum.xplain.xm.workflow.state.VdPhaseContextMutator
import com.solum.xplain.xm.workflow.state.VdPhaseState
import com.solum.xplain.xm.workflow.steps.vd.RefreshProviderValuationsStep
import com.solum.xplain.xm.workflow.steps.vd.StartPhaseStep
import io.atlassian.fugue.Either
import java.time.Clock
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit
import org.bson.types.ObjectId
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Timeout
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
@Timeout(180)
@LogSteps
class VdXmWorkflowServiceIntegrationTest extends IntegrationSpecification implements XmWorkflowServiceIntegrationTest {
  static BigDecimal NAV = BigDecimal.valueOf(10_000d)
  static final String PROVIDER_1 = "TESTPROV"
  static final String PROVIDER_2 = "TESTPROV2"
  static final String TRADE_1 = "TESTTRADE"
  static final String TRADE_2 = "TESTTRADE2"
  public static final String OVERRIDE_P2 = "OVERRIDE_P2"

  @Autowired
  PortfolioItemWriteRepository portfolioItemWriteRepository
  @Autowired
  CompanyIpvSettingsRepository companyIpvSettingsRepository
  @Autowired
  GlobalValuationSettingsRepository globalValuationSettingsRepository
  @Autowired
  IpvDataRepository ipvDataRepository
  @Autowired
  TradeLevelOverrideWriteRepository tradeLevelOverrideWriteRepository
  @Autowired
  XmIpvTasksDefinitionQueryRepository xmIpvTasksDefinitionQueryRepository
  @Autowired
  XmWorkflowService xmWorkflowService
  @Autowired
  ApplicationEventPublisher eventPublisher
  @Autowired
  IpvExceptionManagementCalculationRepository ipvExceptionManagementCalculationRepository
  @Autowired
  DataModificationCommandQueue commandQueue
  @Autowired
  VdExceptionManagementPortfolioFilterBuilder portfoliosFilterBuilder
  @Autowired
  IpvExceptionManagementControllerService controllerService
  @Autowired
  IpvTaskExecutionService taskExecutionService
  @Autowired
  IpvTaskReRunExecutionService taskReRunExecutionService
  @Autowired
  WorkflowService workflowService
  @Autowired
  WorkflowEngine workflowEngine
  @Autowired
  StartPhaseStep startPhaseStep
  @Autowired
  RefreshProviderValuationsStep refreshProviderValuationsStep
  @Autowired
  VdPhaseContextMutator vdPhaseContextMutator
  @Autowired
  DashboardCalculation dashboardCalculation // is a Mock
  @Autowired
  CalculationResultProvider calculationResultProvider // is a Mock
  @Autowired
  CalculationEventProducer calculationEventProducer // is a Mock

  List<Throwable> errorsThrown = new ArrayList<>()

  @TestConfiguration
  static class TestConfig {
    @Bean
    GenericProductTypeProvider genericProductTypeProvider() {
      return new GenericProductTypeProvider()
    }

    @Bean
    NonFxGenericProductDetailsResolver nonFxGenericProductDetailsResolver() {
      return new NonFxGenericProductDetailsResolver()
    }
  }

  static def resolver = user("resolverId")
  static def approver = user("approverId")
  static def fallback = userWithTeams("fallbackId", [new ObjectId(TeamBuilder.TEAM_ID_2)])
  static Authentication approverAuth = new TestingAuthenticationToken(approver, null).tap {
    authenticated = true
  }
  static Authentication resolverAuth = new TestingAuthenticationToken(resolver, null).tap {
    authenticated = true
  }
  static Authentication fallbackAuth = new TestingAuthenticationToken(fallback, null).tap {
    authenticated = true
  }

  def setup() {
    SecurityContextHolder.getContext().authentication = approverAuth
    // If the clock only ticks once per minute then db objects will be created AFTER the phase started and be invalid
    // so change the clock to a normal one.
    def clock = Clock.systemDefaultZone()
    startPhaseStep.configureClock(clock)
    refreshProviderValuationsStep.configureClock(clock)
    vdPhaseContextMutator.configureClock(clock)
    workflowService.setErrorHandler(errorsThrown::add)
    workflowEngine.setIdleTrackingEnabled(true)
    setupTestEventReceiver()
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    mongoOperations.remove(new Query(), IpvTradeResultOverlay)
    mongoOperations.remove(new Query(), StepInstance)
    mongoOperations.remove(new Query(), ProcessExecution)
    mongoOperations.remove(new Query(), Dashboard)
    mongoOperations.remove(new Query(), Team)
    mongoOperations.remove(new Query(), IpvTaskDefaultTeams)
    mongoOperations.remove(new Query(), IpvTasksDefinition)
    mongoOperations.remove(new Query(), IpvBreakTest)
    mongoOperations.remove(new Query(), IpvDataValue)
    mongoOperations.remove(new Query(), CompanyIpvSettings)
    mongoOperations.remove(new Query(), DataProvider)
    mongoOperations.remove(new Query(), IpvDataGroup)
    mongoOperations.remove(new Query(), PortfolioItemEntity)
    mongoOperations.remove(new Query(), PortfolioItem)
    mongoOperations.remove(new Query(), Portfolio)
    mongoOperations.remove(new Query(), CompanyLegalEntity)
    mongoOperations.remove(new Query(), Company)
    mongoOperations.remove(new Query(), ExceptionManagementSettings)
    mongoOperations.remove(new Query(), GlobalValuationSettings)
    mongoOperations.remove(new Query(), CompanyValuationSettings)
    mongoOperations.remove(new Query(), CompanyLegalEntityValuationSettings)
    mongoOperations.remove(new Query(), CurveConfiguration)
    mongoOperations.remove(new Query(), CurveGroup)
    mongoOperations.remove(new Query(), CurveGroupFxRates)
    mongoOperations.remove(new Query(), MarketDataGroup)
    mongoOperations.remove(new Query(), MarketDataProviders)
    mongoOperations.remove(new Query(), MarketDataValue)
    mongoOperations.remove(new Query(), MarketDataKey)
    mongoOperations.remove(new Query(), "fs.files")
    mongoOperations.remove(new Query(), "fs.chunks")
    workflowService.setErrorHandler(null)
    cleanupTestEventReceiver()
  }

  @Unroll("should execute valuation data workflow with vendor valuations (outcome #breakTest.name)")
  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  def shouldExecuteValuationDataWorkflowWithVendorValuations() {
    given: "we have data setup"
    def trade = portfolioItem(TRADE_1)
    def trade2 = portfolioItem(TRADE_2)
    def tMinus1 = LocalDate.now().minusDays(2)
    mongoOperations.insertAll([
      // Curves and configured market data
      xmSettings(xmCurrencyType),
      globalValuationSettings(),
      curveGroup(),
      curveGroupFxRates(),
      curveConfiguration(),
      marketDataGroup(),
      marketDataKey(),
      marketDataValue(BigDecimal.valueOf(tradeCcyReportingCcyFxRate)),
      // Trades
      company(),
      companyLegalEntity(),
      portfolio(),
      emptyPortfolio(),
      trade,
      PortfolioItemBuilder.toData(trade),
      trade2,
      PortfolioItemBuilder.toData(trade2),
      // Pricing slot and provider settings
      ipvDataGroup(),
      dataProvider(PROVIDER_1),
      dataProvider(PROVIDER_2),
      dataProvider(OVERRIDE_P2),
      // Valuation data
      ipvDataValue(TRADE_1, PROVIDER_1),
      ipvDataValue(TRADE_1, PROVIDER_2),
      ipvDataValue(TRADE_2, PROVIDER_1),
      ipvDataValue(TRADE_2, PROVIDER_2),
      ipvDataValue(TRADE_2, OVERRIDE_P2),
      ipvDataValue(TRADE_1, "NAV", navScalingData),
      ipvDataValue(TRADE_2, "NAV", navScalingData),
      // Break test and tsak granularity
      breakTest,
      ipvTasksDefinition(),
      dashboardVd("dashboardId"),
      // T-1 overlay data
      historicValuationDashboard(tMinus1),
      historicOverlayData(TRADE_1, tMinus1, PROVIDER_1, PROVIDER_2),
      historicOverlayData(TRADE_2, tMinus1, PROVIDER_2, OVERRIDE_P2),
    ])
    updateDefaultIpvSettings(PROVIDER_1, PROVIDER_2)
    updateCompanyValuationSettings()

    and: "we have two deal overrides with the same ext id but different portfolios"
    createTradeLevelOverride(TRADE_2, PROVIDER_2)
    createTradeLevelOverride(TRADE_2, "TESTPROV3", "EMPTYPORT"); // nothing should end up with TESTPROV3

    when: "workflow is started and finished"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    if (hasBreak) {
      resolveAndApprove(now, expectedResolution)
    }
    commandQueue.flush()
    waitForVdXmProcessDone()
    waitForDashboardFinalized()

    def results = ipvExceptionManagementCalculationRepository.portfolioResultItemsStream(
    "dashboardId",
    PORTFOLIO_ID,
    IpvExceptionManagementPhase.OVERLAY_1,
    TableFilter.emptyTableFilter()
    ).toList().sort(false) { a ->
      a.tradeInfoExternalTradeId
    }
    def saved = mongoOperations.find(new Query(where(IpvTradeResultOverlay.Fields.dashboardId).is("dashboardId")), IpvTradeResultOverlay)
    .sort(false) { a ->
      a.trade.externalTradeId
    }
    def timeline = xmStepInstanceQueryRepository.getVdDashboardTimelineViews(uniqueId.toString())
    def tasks = xmIpvTasksDefinitionQueryRepository.getDashboardTimelineTaskViews(uniqueId.toString(), LocalDateTime.now(), IpvExceptionManagementPhase.OVERLAY_1, ScrollRequest.unconstrained())

    then: "output data is populated correctly"
    results.size() == 2
    with(results[0]) {
      it.tradeInfoExternalTradeId == TRADE_1
      it.resolvedValue == (hasBreak ? expectedValue : BigDecimal.ONE)
      it.resolutionProviderName == expectedProvider1
      it.resolutionProviderType == expectedProviderType
      it.resolution == (hasBreak ? expectedResolution : null)
      it.calculationCurrency == calcCcy
      it.slaDeadline == SlaDeadline.LDN_COB
    }
    with(results[1]) {
      it.tradeInfoExternalTradeId == TRADE_2
      it.resolvedValue == (hasBreak ? expectedValue : BigDecimal.ONE)
      it.resolutionProviderName == expectedProvider2
      it.resolutionProviderType == expectedProviderType
      it.resolution == (hasBreak ? expectedResolution : null)
      it.calculationCurrency == calcCcy
      it.slaDeadline == SlaDeadline.LDN_COB
    }

    and: "breaks count is correct"
    def breaksView = ipvExceptionManagementCalculationRepository.overlayBreaksCount([])
    breaksView != null
    breaksView.breaksCount == (hasBreak ? 2L : 0L)

    and: "saved data is populated correctly"
    saved.size() == 2
    with(saved[0]) {
      it.trade.externalTradeId == TRADE_1
      it.appliedTestsCount == 1
      it.trade.productType == GenericProductType.CUSTOM_OTHER_1
      it.trade.productGroup == GenericProductGroup.CUSTOM_OTHER
      it.resolution.providerName == expectedProvider1
      it.resolution.providerType == expectedProviderType
      it.phase == IpvExceptionManagementPhase.OVERLAY_1
      it.slaDeadline == SlaDeadline.LDN_COB

      it.hasBreaks == hasBreak
      it.breakTests[0].hasBreak() == hasBreak
      it.maxTriggeredThresholdLevel == (hasBreak ? 1 : null)
      it.resolution.resolution == expectedResolution
      it.resolution.resolutionComment == (hasBreak ? "resolved" : null)
      it.resolution.approvalComment == (hasBreak ? "approved" : null)
      it.resolution.value == (hasBreak ? expectedValue : null)
      it.modifiedBy == (hasBreak ? AuditUser.of(approver) : null)
      it.modifiedAt != null
      if (hasBreak) {
        with(it.previousStatuses) {
          it.size() == 2
          with(it[0]) {
            it.status == EntryResultStatus.WAITING_RESOLUTION
            it.modifiedBy == null
            it.modifiedAt != null
            it.resolutionComment == null
            it.approvalComment == null
          }
          with(it[1]) {
            it.status == EntryResultStatus.WAITING_APPROVAL
            it.modifiedBy == AuditUser.of(resolver)
            it.modifiedAt != null
            it.resolutionComment == "resolved"
            it.approvalComment == null
          }
        }
      }
    }

    and: "timeline contains correct entries"
    timeline.size() == 2
    with(timeline[0]) {
      it.step == DashboardStep.TRADE_DATA_UPLOAD
      it.status == StepStatus.COMPLETED
    }
    with(timeline[1]) {
      it.step == DashboardStep.IPV_OVERLAY_CLEARING
      it.status == StepStatus.COMPLETED
      it.breaksCount == (hasBreak ? 2L : 0L)
    }

    and: "task contains correct entry"
    tasks.size() == 1
    with(tasks[0]) {
      it.breaksCount == (hasBreak ? 2L : 0L)
      it.status == TaskExecutionStatus.APPROVED
      it.type == IpvExceptionManagementPhase.OVERLAY_1
      it.filterSummary.typesSummary() == GenericProductGroup.CUSTOM_OTHER.name()
      if (hasBreak) {
        assert it.performedById == resolver.id
        assert it.performedByName == resolver.name
        assert it.approvedById == approver.id
        assert it.approvedByName == approver.name
      }
    }

    and: "no errors are logged"
    errorsThrown.size() == 0

    when: "dashboard is deleted"
    eventPublisher.publishEvent(DashboardDeletedEvent.newOf("dashboardId"))

    then: "all related data is deleted"
    1 * calculationResultProvider.latestDashboardCalculationsByPortfolioId("dashboardId") >> Map.of()
    0 * calculationEventProducer.publishDeleteEvent(_)
    mongoOperations.find(new Query(), ProcessExecution.class).isEmpty()
    mongoOperations.find(new Query(), StepInstance.class).isEmpty()
    mongoOperations.find(new Query(), IpvTradeResultOverlay.class).size() == 2 // Historic data only.

    where:
    breakTest                                                           || hasBreak | expectedResolution                            | expectedValue  | expectedProviderType          | expectedProvider1       | expectedProvider2          | tradeCcyReportingCcyFxRate | xmCurrencyType                     | calcCcy | navScalingData
    breakTestValueGt("Passing", BigDecimal.ONE)                         || false    | null                                          | null           | IpvProvidersType.P1           | PROVIDER_1              | PROVIDER_2                 | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV
    breakTestValueGt("Keep", BigDecimal.ZERO)                           || true     | TradeResultResolutionType.KEEP                | BigDecimal.ONE | IpvProvidersType.P1           | PROVIDER_1              | PROVIDER_2                 | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV
    breakTestValueGt("Previous", BigDecimal.ZERO)                       || true     | TradeResultResolutionType.PREVIOUS_DAY        | TWO            | IpvProvidersType.P1           | PROVIDER_1              | PROVIDER_2                 | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV
    breakTestValueGt("Switch", BigDecimal.ZERO)                         || true     | TradeResultResolutionType.SWITCH_TO_SECONDARY | BigDecimal.ONE | IpvProvidersType.P2           | PROVIDER_2              | OVERRIDE_P2                | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV
    breakTestValueGt("Cost", BigDecimal.ZERO)                           || true     | TradeResultResolutionType.USE_DEAL_COST       | BigDecimal.TEN | IpvProvidersType.DEAL_COST    | "Trade"                 | "Trade"                    | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV
    breakTestValueGt("Delta", BigDecimal.ZERO)                          || true     | TradeResultResolutionType.DELTA_SECONDARY     | BigDecimal.ONE | IpvProvidersType.MULTIPLE     | "TESTPROV + ΔTESTPROV2" | "TESTPROV2 + ΔOVERRIDE_P2" | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV
    breakTestValueGt("Do Not Price", BigDecimal.ZERO)                   || true     | TradeResultResolutionType.DO_NOT_PRICE        | null           | IpvProvidersType.DO_NOT_PRICE | null                    | null                       | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV
    breakTestValueGt("Manual", BigDecimal.ZERO)                         || true     | TradeResultResolutionType.OVERRIDE_USER       | BigDecimal.TEN | IpvProvidersType.MANUAL       | resolver.name           | resolver.name              | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV
    breakTestValueGtNav("NAV Break", BigDecimal.ONE)                    || false    | null                                          | null           | IpvProvidersType.P1           | PROVIDER_1              | PROVIDER_2                 | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV             // break when (valueDiff * 10_000) / NAV > threshold
    breakTestValueGtNav("NAV Pass", BigDecimal.ONE)                     || true     | TradeResultResolutionType.KEEP                | BigDecimal.ONE | IpvProvidersType.P1           | PROVIDER_1              | PROVIDER_2                 | 1.0d                       | IpvValueCurrencyType.TRADE_CCY     | "USD"   | BigDecimal.ONE
    breakTestValueGtNav("NAV Break FX", BigDecimal.ONE)                 || true     | TradeResultResolutionType.KEEP                | BigDecimal.ONE | IpvProvidersType.P1           | PROVIDER_1              | PROVIDER_2                 | 10d                        | IpvValueCurrencyType.TRADE_CCY     | "USD"   | NAV             // breaks due to fx conversion of NAV
    breakTestValueGtNav("NAV Pass Ccy Type Reporting", BigDecimal.ONE)  || true     | TradeResultResolutionType.KEEP                | BigDecimal.ONE | IpvProvidersType.P1           | PROVIDER_1              | PROVIDER_2                 | 1.0d                       | IpvValueCurrencyType.REPORTING_CCY | "EUR"   | BigDecimal.ONE
    breakTestValueGtNav("NAV Break Ccy Type Reporting", BigDecimal.ONE) || false    | null                                          | null           | IpvProvidersType.P1           | PROVIDER_1              | PROVIDER_2                 | 1.0d                       | IpvValueCurrencyType.REPORTING_CCY | "EUR"   | NAV
  }

  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  def "should keep original value if resolution is rejected"() {
    given: "we have data set up"
    createBaseData()

    when: "workflow is started and finished"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    resolveAndApprove(now, TradeResultResolutionType.SWITCH_TO_SECONDARY, VerificationStatus.REJECTED)
    commandQueue.flush()

    then: "process base value is kept and has meaningful business keys"
    with(mongoOperations.findOne(new Query(where(ProcessExecution.Fields.processId).is(VD_XM_PHASE_PROCESS_ID)), ProcessExecution)) { ProcessExecution<VdPhaseState, VdPhaseContext> it ->
      it.currentState.baseValue == BigDecimal.ONE
      businessKey == "${uniqueId.toString()}-${it.context.trade.key}-1"
      parentBusinessKey == "${uniqueId.toString()}-${it.context.trade.key}"
    }

    when: "different resolution and approved"
    resolveAndApprove(now, TradeResultResolutionType.PREVIOUS_DAY, VerificationStatus.VERIFIED)
    flush()

    then: "process base value is updated"
    waitForVdXmPhaseDone()
    with(mongoOperations.findOne(new Query(where(ProcessExecution.Fields.processId).is(VD_XM_PHASE_PROCESS_ID)), ProcessExecution)) { ProcessExecution<VdPhaseState, VdPhaseContext> it ->
      it.currentState.baseValue == TWO
    }

    and: "no errors are logged"
    errorsThrown.size() == 0
  }

  def "should refuse to open the same task by a second user"() {
    given: "we have data set up"
    createBaseData()

    when: "workflow is started"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)
    def resolutionTaskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_1&stepId=${DECIDE_RESOLUTION_TASK}&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    flush()

    and: "first user starts task"
    waitForActiveStep(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    def claim1 = taskExecutionService.startTasksProgress(resolverAuth, resolutionTaskIds)

    then:
    claim1.isRight()

    when: "second user tries to start same task"
    Either<List<ErrorItem>, List<EntityId>> claim2 = taskExecutionService.startTasksProgress(approverAuth, resolutionTaskIds)

    then:
    claim2.isLeft()

    def left = claim2.left().get() as List<ErrorItem>
    left.size() == 1
    left.get(0).reason == Error.OBJECT_NOT_FOUND
  }

  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  @Unroll("should ignore a second apply resolution (after submitting = #withSubmit)")
  def "should ignore a second apply resolution"() {
    given: "we have data set up"
    createBaseData()

    when: "workflow is started and finished"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    def resolutionTaskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_1&stepId=${DECIDE_RESOLUTION_TASK}&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    flush()
    applyResolution(resolutionTaskIds, TradeResultResolutionType.SWITCH_TO_SECONDARY)
    flush()
    if (withSubmit) {
      submitResolution(resolutionTaskIds)
      commandQueue.flush()
    }
    applyResolution(resolutionTaskIds, TradeResultResolutionType.SWITCH_TO_TERTIARY, false)
    def steps = mongoOperations.find(new Query(where(StepInstance.Fields.processId).is(VD_XM_PHASE_PROCESS_ID).and(StepInstance.Fields.stepId).is(DECIDE_RESOLUTION_TASK)), StepInstance)

    then: "there is only one resolution user task and it has not changed outcome"
    steps.size() == 1
    with(steps[0]) {
      it.outcome.getPropertyValue(VdPhaseState.Fields.resolution).value == TradeResultResolutionType.SWITCH_TO_SECONDARY.name()
    }

    and: "no errors are logged"
    errorsThrown.size() == 0

    where:
    withSubmit << [true, false]
  }

  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  def "should be automatically rejected if resolved to a null value"() {
    given: "we have data set up"
    def trade = portfolioItem(TRADE_1)
    def tMinus1 = LocalDate.now().minusDays(2)
    mongoOperations.insertAll([
      company(),
      companyLegalEntity(),
      portfolio(),
      trade,
      PortfolioItemBuilder.toData(trade),
      ipvDataGroup(),
      dataProvider(PROVIDER_1),
      dataProvider(PROVIDER_2),
      ipvDataValue(TRADE_1, PROVIDER_1, BigDecimal.ONE),
      // No data for provider 2
      breakTestValueGt("Fail", BigDecimal.ZERO),
      ipvTasksDefinition(),
      dashboardVd("dashboardId"),
      historicValuationDashboard(tMinus1),
      historicOverlayData(TRADE_1, tMinus1, PROVIDER_1, PROVIDER_2)
    ])
    updateDefaultIpvSettings(PROVIDER_1, PROVIDER_2)

    when: "workflow is started and finished"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    def resolutionTaskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_1&stepId=${DECIDE_RESOLUTION_TASK}&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    flush()
    applyResolution(resolutionTaskIds, TradeResultResolutionType.SWITCH_TO_SECONDARY)
    flush()
    submitResolution(resolutionTaskIds)
    flush()
    waitForActiveStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)

    then: "process base value is kept"
    with(mongoOperations.findOne(new Query(where(ProcessExecution.Fields.processId).is(VD_XM_PHASE_PROCESS_ID)), ProcessExecution)) { ProcessExecution<VdPhaseState, VdPhaseContext> it ->
      it.currentState.baseValue == BigDecimal.ONE
      it.currentState.approval == VerificationStatus.REJECTED
    }

    and: "there is an active resolution user task"
    with(mongoOperations.findOne(new Query(where(StepInstance.Fields.processId).is(VD_XM_PHASE_PROCESS_ID).and(StepInstance.Fields.status).is(WorkflowStatus.ACTIVE)), StepInstance)) { StepInstance<VdPhaseState> it ->
      it.stepId == DECIDE_RESOLUTION_TASK
      it.initialState.approval == VerificationStatus.REJECTED
    }

    and: "there is an errored resolution apply step"
    with(mongoOperations.findOne(new Query(where(StepInstance.Fields.processId).is(VD_XM_PHASE_PROCESS_ID).and(StepInstance.Fields.status).is(WorkflowStatus.ERROR)), StepInstance)) { StepInstance<VdPhaseState> it ->
      it.stepId == "switchProviderResolution"
      it.initialState.resolution == TradeResultResolutionType.SWITCH_TO_SECONDARY
      it.outcome.isEmpty()
    }

    and: "no errors are logged"
    errorsThrown.size() == 0
  }

  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  def "should remove outcome and reset to ACTIVE if resolution is undone before submitting"() {
    given: "we have data set up"
    createBaseData()

    when: "workflow is started and outcome undone"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    def resolutionTaskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_1&stepId=${DECIDE_RESOLUTION_TASK}&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    flush()
    if (interimStatus == WorkflowStatus.FINALIZING) {
      applyResolution(resolutionTaskIds, TradeResultResolutionType.KEEP)
      waitForFinalizingStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    } else {
      applyHold(resolutionTaskIds, "HOLD_REASON")
      waitForHeldStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    }
    flush()
    // Find the finalizing step
    def step = mongoOperations.findOne(new Query(where(StepInstance.Fields.processId).is(VD_XM_PHASE_PROCESS_ID).and(StepInstance.Fields.stepId).is(DECIDE_RESOLUTION_TASK).and(StepInstance.Fields.status).is(interimStatus)), StepInstance)
    controllerService.undoResolution(resolverAuth, resolutionTaskIds, new ResultDisplayFilterForm(), TableFilter.emptyTableFilter(), [step.businessKey])
    flush()
    waitForActiveStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)

    then: "process base value is kept"
    with(mongoOperations.findOne(new Query(where(StepInstance.Fields.id).is(step.id)), StepInstance)) {
      outcome.size() == 0
      status == WorkflowStatus.ACTIVE
    }

    and: "no errors are logged"
    errorsThrown.size() == 0

    where:
    interimStatus << [WorkflowStatus.FINALIZING, WorkflowStatus.HELD]
  }

  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  def "should remove assignee and outcome and reset to ACTIVE if resolution is cancelled before submitting"() {
    given: "we have data set up"
    createBaseData()

    when: "workflow is started and cancelled after resolution set"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    def resolutionTaskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_1&stepId=${DECIDE_RESOLUTION_TASK}&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    flush()
    if (interimStatus == WorkflowStatus.FINALIZING) {
      applyResolution(resolutionTaskIds, TradeResultResolutionType.KEEP)
      waitForFinalizingStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    } else {
      applyHold(resolutionTaskIds, "HOLD_REASON")
      waitForHeldStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    }
    flush()
    // Find the finalizing step
    def step = mongoOperations.findOne(new Query(where(StepInstance.Fields.processId).is(VD_XM_PHASE_PROCESS_ID).and(StepInstance.Fields.stepId).is(DECIDE_RESOLUTION_TASK).and(StepInstance.Fields.status).is(interimStatus)), StepInstance)
    taskExecutionService.cancelOwnTask(resolverAuth, resolutionTaskIds[0])
    flush()
    waitForActiveStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)

    then: "process base value is kept"
    with(mongoOperations.findOne(new Query(where(StepInstance.Fields.id).is(step.id)), StepInstance)) {
      outcome.size() == 0
      assignee == null
      status == WorkflowStatus.ACTIVE
      modifiedAt == startedAt
    }

    and: "no errors are logged"
    errorsThrown.size() == 0

    and: "process hold reason is cleared"
    with(mongoOperations.findOne(new Query(where(ProcessExecution.Fields.processId).is(VD_XM_PHASE_PROCESS_ID)
    .and(ProcessExecution.Fields.businessKey).is(step.businessKey)), ProcessExecution)) {
      holdReason == null
    }

    where:
    interimStatus << [WorkflowStatus.FINALIZING, WorkflowStatus.HELD]
  }

  def "should run calculation if XPLAIN is a provider"() {
    given: "we have data set up"

    def trade = portfolioItem(TRADE_1)
    mongoOperations.insertAll([
      xmSettings(IpvValueCurrencyType.TRADE_CCY),
      globalValuationSettings(),
      marketDataGroup(),
      company(),
      companyLegalEntity(),
      portfolio(),
      trade,
      PortfolioItemBuilder.toData(trade),
      ipvDataGroup(),
      dashboardVd("dashboardId"),
      breakTestValueGt("Pass", BigDecimal.TEN),
      ipvTasksDefinition()
    ])
    updateDefaultIpvSettings("XPLAIN")
    updateCompanyValuationSettings()

    def now = BitemporalDate.newOfNow()
    def calculationId = ObjectId.get()
    def calculationResult = new PortfolioItemCalculatedExcMngmntView(
    tradeInfoExternalTradeId: TRADE_1,
    metricsPresentValuePayLegCurrency: BigDecimal.ONE,
    metricsPresentValue: BigDecimal.TEN,
    tradeInfoTradeType: CoreProductType.INFLATION,
    metricsInf01: BigDecimal.TEN,
    metricsPvVega: BigDecimal.ZERO,
    metricsBreakevenParRate: BigDecimal.ZERO,
    metricsBreakevenImpliedVol: BigDecimal.ZERO
    )
    calculationResultProvider.latestDashboardCalculation(PORTFOLIO_ID, _ as LocalDate, MDG_ID) >> Optional.of(calculationId)
    calculationResultProvider.calculationResultExceptionMngmntItems(calculationId) >> List.of(calculationResult)

    when: "workflow is started and finished"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now) // but doesn't create the dashboard nor verify it exists
    waitForActiveStep(VD_XM_CALC_PROCESS_ID, "waitForCalculation")

    then: "a calculation is run"
    1 * dashboardCalculation.calculate("dashboardId", now.getActualDate(), AuditUser.of(approver),
    _ as CompanyLegalEntityValuationSettingsView, _ as PortfolioCondensedView, MarketDataSourceType.RAW_PRIMARY, now,
    [GenericProductType.CUSTOM_OTHER_1]) >> Either.right(EntityId.entityId(calculationId))

    when: "the calculation is completed"
    eventPublisher.publishEvent(TradesCalculationFinishedEvent.newOf(calculationId, new ObjectId(PORTFOLIO_ID), "dashboardId", null, null, CalculationStatus.FINISHED))
    flush()
    waitForVdXmPhaseDone()
    def timeline = xmStepInstanceQueryRepository.getVdDashboardTimelineViews(uniqueId.toString())

    then: "the calculation is shown on the timeline"
    timeline.size() == 3
    with(timeline[1]) {
      it.step == DashboardStep.OPV_VALUATIONS
      it.status == StepStatus.COMPLETED
      !it.finishedAt.isBefore(it.startedAt)
      it.portfolios.size() == 1
      it.portfolios[0].name == PORTFOLIO_EXTERNAL_ID
    }

    and: "the calculation result was fetched along with provider valuations"
    waitForVdXmProcessDone()
    waitForDashboardFinalized()
    with(ipvExceptionManagementCalculationRepository.portfolioResultItemsStream(
    "dashboardId",
    PORTFOLIO_ID,
    IpvExceptionManagementPhase.OVERLAY_1,
    TableFilter.emptyTableFilter()
    ).toList().sort(false) { a ->
      a.tradeInfoExternalTradeId
    }) {
      it.size() == 1
      it[0].resolvedValue == BigDecimal.ONE
    }

    when: "the dashboard is deleted"
    eventPublisher.publishEvent(DashboardDeletedEvent.newOf("dashboardId"))

    then: "the calculation result is deleted"
    1 * calculationResultProvider.latestDashboardCalculationsByPortfolioId("dashboardId") >> [(PORTFOLIO_ID): calculationId]
    1 * calculationEventProducer.publishDeleteEvent(calculationId.toHexString())
  }

  @Unroll("should fetch new valuation data if we rerun after completion (#rerunType, updated result #expectUpdatedResultData, re-run outcome #breakTest.name)")
  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  def shouldFetchNewValuationDataIfRerunAfterCompletion() {
    given:
    def trade = portfolioItem(TRADE_1)
    def ipvDataValue = ipvDataValue(TRADE_1, PROVIDER_1, BigDecimal.ONE)
    mongoOperations.insertAll([
      company(),
      companyLegalEntity(),
      portfolio(),
      trade,
      PortfolioItemBuilder.toData(trade),
      ipvDataGroup(),
      dataProvider(PROVIDER_1),
      ipvDataValue,
      dashboardVd("dashboardId"),
      breakTest,
      ipvTasksDefinition()
    ])
    updateDefaultIpvSettings(PROVIDER_1)

    when: "workflow is started and finished"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    waitForVdXmProcessDone()
    def originalResultId = ipvExceptionManagementCalculationRepository.portfolioResultItemsStream(
    "dashboardId",
    PORTFOLIO_ID,
    IpvExceptionManagementPhase.OVERLAY_1,
    TableFilter.emptyTableFilter()
    ).findFirst().get().id

    and: "we have new valuation data"
    ipvDataRepository.updateValue(ipvDataValue.id, new IpvDataProviderValueUpdateForm(
    value: updatedValue,
    delta: BigDecimal.TEN,
    vega: BigDecimal.ZERO,
    versionForm: NewMinorVersionForm.of("test")
    ))

    and: "we rerun the task"
    resetTestEventReceiver()
    def taskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_1&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    taskReRunExecutionService.rerunTasks(resolverAuth, new IpvTaskExecutionRerunRequest(taskIds, rerunType, false))
    flush()
    if (expectResultDataCount > 0) {
      waitForVdXmProcessDone()
    } else {
      waitForActiveStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    }

    then: "result data has been updated"
    with(ipvExceptionManagementCalculationRepository.portfolioResultItemsStream(
    "dashboardId",
    PORTFOLIO_ID,
    IpvExceptionManagementPhase.OVERLAY_1,
    TableFilter.emptyTableFilter()
    ).toList().sort(false) { a ->
      a.tradeInfoExternalTradeId
    }) {
      it.size() == expectResultDataCount
      if (expectResultDataCount > 0) {
        assert it[0].resolvedValue == updatedValue
        assert expectUpdatedResultData ^ it[0].id == originalResultId
      }

      and: "no errors are logged"
      errorsThrown.size() == 0
    }

    where:
    breakTest                                   | rerunType                        | updatedValue    || expectResultDataCount | expectUpdatedResultData
    breakTestValueGt("Passing", BigDecimal.ONE) | IpvTaskExecutionRerunType.DELETE | BigDecimal.ZERO || 1                     | true
    breakTestValueGt("Passing", BigDecimal.ONE) | IpvTaskExecutionRerunType.UPDATE | BigDecimal.ZERO || 1                     | true
    breakTestValueGt("Passing", BigDecimal.ONE) | IpvTaskExecutionRerunType.DELETE | BigDecimal.ONE  || 1                     | true
    breakTestValueGt("Passing", BigDecimal.ONE) | IpvTaskExecutionRerunType.UPDATE | BigDecimal.ONE  || 1                     | false
    breakTestValueGt("Failing", BigDecimal.ONE) | IpvTaskExecutionRerunType.UPDATE | TWO             || 0                     | false
  }

  @Unroll("should fetch new valuation data if we rerun after completion (#rerunType, with Overlay II, re-run outcome #breakTest.name)")
  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  def shouldFetchNewValuationDataIfRerunAfterCompletionWithOverlay2() {
    given:
    def trade = portfolioItem(TRADE_1)
    def currentIpvDataValue = ipvDataValue(TRADE_1, PROVIDER_2, initialSecondaryProviderData)
    mongoOperations.insertAll([
      company(),
      companyLegalEntity(),
      portfolio(),
      trade,
      PortfolioItemBuilder.toData(trade),
      ipvDataGroup(),
      dataProvider(PROVIDER_1),
      dataProvider(PROVIDER_2),
      ipvDataValue(TRADE_1, PROVIDER_1, BigDecimal.ONE),
      currentIpvDataValue,
      dashboardVd("dashboardId"),
      breakTestValueGt("Pass", BigDecimal.ONE),
      breakTest,
      ipvTasksDefinition(),
      ipvTasksDefinition(IpvExceptionManagementPhase.OVERLAY_2),
    ])
    updateDefaultIpvSettings(PROVIDER_1, PROVIDER_2)

    when: "workflow is started and finished"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    if (waitForDone) {
      waitForVdXmProcessDone()
    } else {
      waitForActiveStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    }

    and: "we have new valuation data"
    ipvDataRepository.updateValue(currentIpvDataValue.id, new IpvDataProviderValueUpdateForm(
    value: secondaryProviderData,
    delta: BigDecimal.TEN,
    vega: BigDecimal.ZERO,
    versionForm: NewMinorVersionForm.of("test")
    ))
    flush()
    and: "we rerun the task"
    resetTestEventReceiver()
    def taskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_2&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    taskReRunExecutionService.rerunTasks(resolverAuth, new IpvTaskExecutionRerunRequest(taskIds, rerunType, false))

    if (expectResultDataCount > 0) {
      waitForVdXmProcessDone()
    } else {
      waitForActiveStep(VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    }

    then: "result data has been updated"
    with(ipvExceptionManagementCalculationRepository.portfolioResultItemsStream(
    "dashboardId",
    PORTFOLIO_ID,
    IpvExceptionManagementPhase.OVERLAY_2,
    TableFilter.emptyTableFilter()
    ).toList().sort(false) { a -> a.tradeInfoExternalTradeId }) {
      it.size() == expectResultDataCount
      if (expectResultDataCount > 0) {
        assert it[0].resolvedValue == secondaryProviderData
      }

      and: "no errors are logged"
      errorsThrown.size() == 0
    }

    where:
    breakTest                                                                         | rerunType                        | initialSecondaryProviderData | secondaryProviderData || expectResultDataCount | waitForDone
    breakTestValueSameValueProvider("Passing", IpvExceptionManagementPhase.OVERLAY_2) | IpvTaskExecutionRerunType.DELETE | BigDecimal.ONE               | BigDecimal.ONE        || 1                     | true
    breakTestValueSameValueProvider("Passing", IpvExceptionManagementPhase.OVERLAY_2) | IpvTaskExecutionRerunType.DELETE | TWO                          | BigDecimal.ONE        || 1                     | false
    breakTestValueSameValueProvider("Passing", IpvExceptionManagementPhase.OVERLAY_2) | IpvTaskExecutionRerunType.UPDATE | TWO                          | BigDecimal.ONE        || 1                     | false
    breakTestValueSameValueProvider("Failing", IpvExceptionManagementPhase.OVERLAY_2) | IpvTaskExecutionRerunType.UPDATE | TWO                          | TWO                   || 0                     | false
  }

  @Unroll("should fail to re-run items that are held (rerunType: #rerunType, applyOnHold: #applyOnHold)")
  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  def shouldFailToReRunItemsThatAreHeld() {
    given: "we have data set up"
    createBaseData()

    when: "workflow is started and outcome undone"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    def resolutionTaskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_1&stepId=${DECIDE_RESOLUTION_TASK}&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    flush()

    and: "hold resolution is conditionally applied"
    if (applyOnHold) {
      applyHold(resolutionTaskIds, "HOLD_REASON")
    } else {
      waitForActiveStep(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    }
    flush()

    then: "when we rerun the task"
    def result = taskReRunExecutionService.rerunTasks(resolverAuth, new IpvTaskExecutionRerunRequest(resolutionTaskIds, rerunType, false))
    if (applyOnHold) {
      result.isLeft()
      ((List<ErrorItem>) result.left().get()).size() == 1
      ((List<ErrorItem>) result.left().get()).get(0).description == "No tasks found to which this action could be applied."
    } else {
      result.isRight()
    }

    where:
    rerunType                        | applyOnHold
    IpvTaskExecutionRerunType.DELETE | true
    IpvTaskExecutionRerunType.UPDATE | true
    IpvTaskExecutionRerunType.DELETE | false
    IpvTaskExecutionRerunType.UPDATE | false
  }

  // If this times out, it will mean the async stuff has got stuck somewhere as it should run quickly.
  def "should move steps into second phase if first phase all passes"() {
    given: "we have data set up"
    def trade = portfolioItem(TRADE_1)
    mongoOperations.insertAll([
      company(),
      companyLegalEntity(),
      portfolio(),
      trade,
      PortfolioItemBuilder.toData(trade),
      ipvDataGroup(),
      dataProvider(PROVIDER_1),
      ipvDataValue(TRADE_1, PROVIDER_1, BigDecimal.ONE),
      breakTestValueGt("Pass", BigDecimal.ONE),
      breakTestValueGt("Fail", BigDecimal.ZERO, IpvExceptionManagementPhase.OVERLAY_2),
      dashboardVd("dashboardId"),
      ipvTasksDefinition(),
      ipvTasksDefinition(IpvExceptionManagementPhase.OVERLAY_2),
    ])
    updateDefaultIpvSettings(PROVIDER_1)

    when: "workflow is started and finished"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    URI uniqueId = xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)
    waitForActiveStep(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    flush()

    def results1 = ipvExceptionManagementCalculationRepository.portfolioResultItemsStream(
    "dashboardId",
    PORTFOLIO_ID,
    IpvExceptionManagementPhase.OVERLAY_1,
    TableFilter.emptyTableFilter()
    ).toList().sort(false) { a ->
      a.tradeInfoExternalTradeId
    }
    def results2 = ipvExceptionManagementCalculationRepository.portfolioResultItemsStream(
    "dashboardId",
    PORTFOLIO_ID,
    IpvExceptionManagementPhase.OVERLAY_2,
    TableFilter.emptyTableFilter()
    ).toList().sort(false) { a ->
      a.tradeInfoExternalTradeId
    }
    def saved1 = mongoOperations.find(
    new Query(where(IpvTradeResultOverlay.Fields.dashboardId).is("dashboardId")
    .and(IpvTradeResultOverlay.Fields.phase).is(IpvExceptionManagementPhase.OVERLAY_1)), IpvTradeResultOverlay)
    .sort(false) { a ->
      a.trade.externalTradeId
    }
    def saved2 = mongoOperations.find(
    new Query(where(IpvTradeResultOverlay.Fields.dashboardId).is("dashboardId")
    .and(IpvTradeResultOverlay.Fields.phase).is(IpvExceptionManagementPhase.OVERLAY_2)), IpvTradeResultOverlay)
    .sort(false) { a ->
      a.trade.externalTradeId
    }
    def timeline = xmStepInstanceQueryRepository.getVdDashboardTimelineViews(uniqueId.toString())
    def tasks1 = xmIpvTasksDefinitionQueryRepository.getDashboardTimelineTaskViews(uniqueId.toString(), LocalDateTime.now(), IpvExceptionManagementPhase.OVERLAY_1, ScrollRequest.unconstrained())
    def tasks2 = xmIpvTasksDefinitionQueryRepository.getDashboardTimelineTaskViews(uniqueId.toString(), LocalDateTime.now(), IpvExceptionManagementPhase.OVERLAY_2, ScrollRequest.unconstrained())
    def userTasks1 = xmIpvTasksDefinitionQueryRepository.getUserTaskViews(LocalDateTime.now(), IpvExceptionManagementPhase.OVERLAY_1, approver.id, approver.teams, null, LocalDateTime.now(), TableFilter.emptyTableFilter(), ScrollRequest.unconstrained())
    def userTasks2 = xmIpvTasksDefinitionQueryRepository.getUserTaskViews(LocalDateTime.now(), IpvExceptionManagementPhase.OVERLAY_2, approver.id, approver.teams, null, LocalDateTime.now(), TableFilter.emptyTableFilter(), ScrollRequest.unconstrained())

    then: "second phase is waiting for approval"
    with(mongoOperations.findOne(new Query(where(ProcessExecution.Fields.processId).is(VD_XM_PHASE_PROCESS_ID).and(PathUtils.joinPaths(ProcessExecution.Fields.context, VdPhaseContext.Fields.phase)).is(IpvExceptionManagementPhase.OVERLAY_2)), ProcessExecution)) { ProcessExecution<VdPhaseState, VdPhaseContext> it ->
      it != null
      it.businessKey == "${uniqueId.toString()}-${it.context.trade.key}-2"
    }

    and: "output data is populated correctly"
    results1.size() == 1
    results2.size() == 0

    and: "saved data is populated correctly"
    saved1.size() == 1
    saved2.size() == 0

    and: "timeline contains correct entries"
    timeline.size() == 3
    with(timeline[0]) {
      it.step == DashboardStep.TRADE_DATA_UPLOAD
      it.status == StepStatus.COMPLETED
      !it.finishedAt.isBefore(it.startedAt)
    }
    with(timeline[1]) {
      it.step == DashboardStep.IPV_OVERLAY_CLEARING
      it.status == StepStatus.COMPLETED
      it.breaksCount == 0L
      !it.finishedAt.isBefore(it.startedAt)
      !it.startedAt.isBefore(timeline[0].finishedAt)
    }
    with(timeline[2]) {
      it.step == DashboardStep.IPV_OVERLAY_CLEARING_2
      it.status == StepStatus.IN_PROGRESS
      it.breaksCount == 1L
      it.finishedAt == null
      !it.startedAt.isBefore(timeline[1].finishedAt)
    }

    and: "dashboard tasks contain correct entries"
    tasks1.size() == 1
    tasks2.size() == 1
    with(tasks1[0]) {
      it.breaksCount == 0L
      it.status == TaskExecutionStatus.APPROVED
      it.type == IpvExceptionManagementPhase.OVERLAY_1
      it.filterSummary.typesSummary() == GenericProductGroup.CUSTOM_OTHER.name()
      !it.finishedAt.isBefore(it.startedAt)
    }
    with(tasks2[0]) {
      it.breaksCount == 1L
      it.status == TaskExecutionStatus.NOT_STARTED
      it.type == IpvExceptionManagementPhase.OVERLAY_2
      it.filterSummary.typesSummary() == GenericProductGroup.CUSTOM_OTHER.name()
      it.finishedAt == null
      !it.startedAt.isBefore(tasks1[0].finishedAt)
    }

    and: "user tasks contain correct entries"
    userTasks1.size() == 0
    userTasks2.size() == 1
    with(userTasks2[0]) {
      it.breaksCount == 1L
      it.status == TaskExecutionStatus.NOT_STARTED
      it.type == IpvExceptionManagementPhase.OVERLAY_2
      it.filterSummary.typesSummary() == GenericProductGroup.CUSTOM_OTHER.name()
      it.finishedAt == null
    }

    and: "no errors are logged"
    errorsThrown.size() == 0
  }

  def "should complete successfully when no VD providers mapped"() {
    given: "minimal setup: portfolio and dashboard definition, but no provider mapping"
    def testDashboardId = "dashboardIdNoProvidersSimple"
    def testTrade = portfolioItem(TRADE_1)
    mongoOperations.insertAll([
      company(),
      companyLegalEntity(),
      portfolio(),
      testTrade,
      PortfolioItemBuilder.toData(testTrade),
      ipvDataGroup(),
      dashboardVd(testDashboardId),
      ipvTasksDefinition(),
      globalValuationSettings()
    ])

    when: "valuation data dashboard workflow is started"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()
    URI uniqueId = xmWorkflowService.startValuationDataDashboard(testDashboardId, setup, now)
    String businessKey = uniqueId.toString()

    and: "workflow is allowed to finish (waiting only for the parent process)"
    waitForVdXmProcessDone()
    commandQueue.flush()

    then: "the main dashboard process completed successfully"
    def mainProcess = mongoOperations.findOne(new Query(where(ProcessExecution.Fields.businessKey).is(businessKey)), ProcessExecution)
    mainProcess != null
    mainProcess.status == WorkflowStatus.DONE

    and: "'callEntryStep' completed successfully"
    def callEntryStep = mongoOperations.findOne(new Query(
    (where(StepInstance.Fields.rootBusinessKey).is(businessKey) & StepInstance.Fields.stepId).is("callVdEntry")
    ), StepInstance)
    callEntryStep != null
    callEntryStep.status == WorkflowStatus.DONE

    and: "No trade entry subprocesses were created"
    def tradeSubprocessesCount = mongoOperations.count(new Query(
    (where(ProcessExecution.Fields.rootBusinessKey).is(businessKey) & ProcessExecution.Fields.processId).is(VdXmWorkflowProvider.VD_XM_TRADE_PROCESS_ID)
    ), ProcessExecution.class)
    tradeSubprocessesCount == 0

    and: "No unexpected errors were logged"
    errorsThrown.size() == 0
  }

  private void resolveAndApprove(BitemporalDate now, TradeResultResolutionType expectedResolution, VerificationStatus expectedApproval = VerificationStatus.VERIFIED) {
    def resolutionTaskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_1&stepId=${DECIDE_RESOLUTION_TASK}&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    applyResolution(resolutionTaskIds, expectedResolution)
    flush()
    submitResolution(resolutionTaskIds)
    def approvalTaskIds = [
      "ipvDataGroupId=${IPV_DATA_GROUP_ID}&phase=OVERLAY_1&stepId=${APPROVE_RESOLUTION_TASK}&valuationDate=${now.actualDate.toString()}T00:00:00.000Z&granularityByTradeType=${GenericProductGroup.CUSTOM_OTHER.name()}&".toString()
    ]
    applyAndSubmitApproval(approvalTaskIds, expectedApproval)
  }

  def "should allocate tasks using global default teams when no product-specific team is defined for OVERLAY_1"() {
    given: "we have base data set up"
    createBaseData()

    and: "no product-specific team is available"
    mongoOperations.updateFirst(new Query(), new Update().unset(IpvTasksDefinition.Fields.teams), IpvTasksDefinition)

    and: "necessary workflow setup is created"
    VdExceptionManagementSetup setup = VdExceptionManagementSetup.newOf(
    portfoliosFilterBuilder.build([new PricingSlotPortfolioForm(PORTFOLIO_ID, PricingSlot.LDN_1500)]),
    [portfolioDashboardSettings()]
    )
    def now = BitemporalDate.newOfNow()

    and: "workflow is started"
    xmWorkflowService.startValuationDataDashboard("dashboardId", setup, now)

    when: "user tasks data for OVERLAY_1 are retrieved"
    waitForActiveStep(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    flush()
    def overlay1Tasks = xmIpvTasksDefinitionQueryRepository.getUserTaskViews(
    LocalDateTime.now(),
    IpvExceptionManagementPhase.OVERLAY_1,
    fallback.id,
    fallback.teams,
    null,
    LocalDateTime.now(),
    TableFilter.emptyTableFilter(),
    ScrollRequest.unconstrained()
    )

    then: "Overlay 1 tasks use global default resolution team"
    !overlay1Tasks.empty
    overlay1Tasks.every {
      it.getResolutionTeams()*.id == [TeamBuilder.TEAM_ID_2]
    }

    when: "ineligible user starts task"
    def claim1 = taskExecutionService.startTasksProgress(resolverAuth, overlay1Tasks*.id)

    then: "task cannot be claimed"
    claim1.isLeft()

    when: "global default team user starts task"
    def claim2 = taskExecutionService.startTasksProgress(fallbackAuth, overlay1Tasks*.id)

    then: "task is claimed successfully"
    claim2.isRight()
  }

  private void applyResolution(ArrayList<String> resolutionTaskIds, TradeResultResolutionType expectedResolution, boolean waitUntilActive = true) {
    if (waitUntilActive) waitForActiveStep(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    taskExecutionService.startTasksProgress(resolverAuth, resolutionTaskIds)
    controllerService.applyResolution(
    resolverAuth,
    new ApplyResolutionForm(
    resolutionTaskIds,
    new ResolutionForm(expectedResolution, null, expectedResolution == TradeResultResolutionType.OVERRIDE_USER ? BigDecimal.TEN : null, "resolved"),
    [],
    ),
    TableFilter.parseParameters(["tradeInfoTradeTypeGroup,equal,CUSTOM_OTHER", "type,equal,CUSTOM_OTHER_1"]),
    null
    )
  }

  private void applyHold(ArrayList<String> resolutionTaskIds, String holdReason, boolean waitUntilActive = true) {
    if (waitUntilActive) waitForActiveStep(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    taskExecutionService.startTasksProgress(resolverAuth, resolutionTaskIds)
    controllerService.applyResolution(
    resolverAuth,
    new ApplyResolutionForm(
    resolutionTaskIds,
    new ResolutionForm(TradeResultResolutionType.HOLD, new TradeResultResolutionSubTypeReference(holdReason),
    null, "waiting"),
    []
    ),
    TableFilter.parseParameters(["tradeInfoTradeTypeGroup,equal,CUSTOM_OTHER", "type,equal,CUSTOM_OTHER_1"]),
    new MockMultipartFile("file", "test.csv", "text/csv", "resolve".bytes)
    )
  }

  private void submitResolution(ArrayList<String> resolutionTaskIds) {
    waitForFinalizingStep(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID, DECIDE_RESOLUTION_TASK)
    taskExecutionService.submitTasksResolution(resolverAuth, resolutionTaskIds)
  }

  private void applyAndSubmitApproval(ArrayList<String> approvalTaskIds, VerificationStatus expectedApproval) {
    waitForActiveStep(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID, APPROVE_RESOLUTION_TASK)
    taskExecutionService.startTasksApproval(approverAuth, approvalTaskIds)
    controllerService.verifyResolutions(approverAuth, new ApplyVerificationForm(
    [],
    new VerificationForm(expectedApproval, expectedApproval == VerificationStatus.VERIFIED ? "approved" : "rejected"),
    approvalTaskIds
    ), null, TableFilter.emptyTableFilter())
    flush()
    waitForFinalizingStep(VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID, APPROVE_RESOLUTION_TASK)
    taskExecutionService.submitTasksApproval(approverAuth, approvalTaskIds)
  }

  private void flush() {
    // wait for user task to be ready
    workflowEngine.waitUntilIdle(500, TimeUnit.MILLISECONDS)
    // make sure the database is up to date
    commandQueue.flush()
  }

  private void createBaseData() {
    def trade = portfolioItem(TRADE_1)
    def tMinus1 = LocalDate.now().minusDays(2)
    mongoOperations.insertAll([
      company(),
      companyLegalEntity(),
      portfolio(),
      trade,
      PortfolioItemBuilder.toData(trade),
      ipvDataGroup(),
      dataProvider(PROVIDER_1),
      dataProvider(PROVIDER_2),
      ipvDataValue(TRADE_1, PROVIDER_1, BigDecimal.ONE),
      ipvDataValue(TRADE_1, PROVIDER_2, TWO),
      breakTestValueGt("Fail", BigDecimal.ZERO),
      *teams(),
      ipvTaskDefaultTeams(),
      ipvTasksDefinition(),
      dashboardVd("dashboardId"),
      historicValuationDashboard(tMinus1),
      historicOverlayData(TRADE_1, tMinus1, PROVIDER_1, PROVIDER_2)
    ])
    updateDefaultIpvSettings(PROVIDER_1, PROVIDER_2)
  }

  private static IpvBreakTest breakTestValueGt(String name, BigDecimal threshold, IpvExceptionManagementPhase phase = IpvExceptionManagementPhase.OVERLAY_1) {
    IpvBreakTest.newOf().tap {
      it.name = name
      it.scope = phase
      it.enabled = true
      it.tradeFilter = new TradeFilter()
      it.providersTypes = [IpvProvidersType.P1]
      it.measureType = IpvMeasureType.VALUE
      it.type = IpvTestType.VALUE
      it.operator = Operator.GT
      it.threshold = [threshold]
    }
  }

  private static IpvBreakTest breakTestValueSameValueProvider(String name, IpvExceptionManagementPhase phase = IpvExceptionManagementPhase.OVERLAY_1) {
    IpvBreakTest.newOf().tap {
      it.name = name
      it.scope = phase
      it.enabled = true
      it.tradeFilter = new TradeFilter()
      it.measureType = IpvMeasureType.GREEKS_DV01
      it.type = IpvTestType.PRIMARY_VS_SECONDARY
      it.operator = Operator.GT
      it.threshold = [0.02, 0.05, 0.07]
    }
  }

  private static IpvBreakTest breakTestValueGtNav(String name, BigDecimal threshold, IpvExceptionManagementPhase phase = IpvExceptionManagementPhase.OVERLAY_1) {
    IpvBreakTest.newOf().tap {
      it.name = name
      it.scope = phase
      it.enabled = true
      it.tradeFilter = new TradeFilter()
      it.providersTypes = [IpvProvidersType.P1]
      it.measureType = IpvMeasureType.NAV
      it.type = IpvTestType.DAY_TO_DAY
      it.operator = Operator.GT
      it.threshold = [threshold]
    }
  }

  private void updateDefaultIpvSettings(String primaryProvider, String secondaryProvider = null) {
    eventPublisher.publishEvent(new CompanyCreated(COMPANY_ID))
    eventPublisher.publishEvent(new CompanyLegalEntityCreated(COMPANY_ID, LEGAL_ENTITY_ID))

    companyIpvSettingsRepository.updateDefaultIpvSettings(COMPANY_ID, NewVersionFormV2.ROOT_DATE,
    new IpvValuationSettingsForm(
    SlaDeadline.LDN_COB,
    Map.of(GenericProductType.CUSTOM_OTHER_1, new IpvValuationProvidersForm(
    IPV_DATA_GROUP_ID, primaryProvider, secondaryProvider, null, null
    )),
    NewVersionFormV2.newDefault()
    )
    ).getOrError {
      "could not insert IPV settings"
    }
  }

  private EntityId createTradeLevelOverride(String externalTradeId, String primaryProvider, String externalPortfolioId = PORTFOLIO_EXTERNAL_ID) {
    tradeLevelOverrideWriteRepository.insert(new TradeLevelOverrideForm(
    externalCompanyId: COMPANY_EXTERNAL_ID,
    externalEntityId: LEGAL_ENTITY_EXTERNAL_ID,
    externalPortfolioId: externalPortfolioId,
    externalPortfolioItemId: externalTradeId,
    providers: new ValuationDataProvidersForm(
    primaryProvider, OVERRIDE_P2, "Override P3", "Override P4"
    ),
    versionForm: NewVersionFormV2.newDefault()
    ))
  }
}
