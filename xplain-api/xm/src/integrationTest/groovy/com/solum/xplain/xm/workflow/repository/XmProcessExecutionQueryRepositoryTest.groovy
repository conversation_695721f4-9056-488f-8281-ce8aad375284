package com.solum.xplain.xm.workflow.repository

import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FX_RATE
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getCAP_FLOOR_VOL_USD_3M_VOLS
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getCDS_1Y
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getFIXED_IBOR_SWAP_EUR_6M
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getFIXED_INFLATION_SWAP_EUR_EXT_CPI
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getFIXED_OVERNIGHT_SWAP_EUR_EONIA_OIS
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getFRA_EUR_6M
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getFX_RATE_EUR_USD
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getFX_SWAP_EUR_USD
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getFX_VOL_EUR_USD
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getFX_VOL_SKEW_EUR_USD
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getIBOR_FIXING_DEPOSIT_EUR_6M
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getIBOR_FUTURE_EUR_3M
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getIBOR_IBOR_SWAP
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getOVERNIGHT_IBOR_BASIS_SWAP
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getSWAPTION_ATM_EUR_6M_VOLS
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getSWAPTION_SKEW_USD_3M_VOLS
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder.getXCCY_IBOR_IBOR_SWAP_EUR_VS_USD
import static com.solum.xplain.workflow.entity.ProcessExecution.PROCESS_EXECUTION_COLLECTION
import static com.solum.xplain.xm.workflow.MdXmWorkflowProvider.MD_XM_KEY_PROCESS_ID
import static com.solum.xplain.xm.workflow.MdXmWorkflowProvider.MD_XM_PRELIMINARY_PROCESS_ID
import static com.solum.xplain.xm.workflow.MdXmWorkflowProvider.MD_XM_PROCESS_ID
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.VD_XM_PROCESS_ID

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.value.IpvDataGroupVo
import com.solum.xplain.core.company.value.ProvidersVo
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.mdvalue.value.ValueBidAskType
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.CoreProductTypeGroup
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.extensions.enums.CreditSector
import com.solum.xplain.generic.type.product.GenericProductGroup
import com.solum.xplain.generic.type.product.GenericProductType
import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.value.WorkflowStatus
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementResultMapper
import com.solum.xplain.xm.excmngmt.process.data.Instrument
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.tasks.entity.IpvTasksDefinition
import com.solum.xplain.xm.tasks.entity.TasksDefinition
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByAssetClassType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByContractualTerm
import com.solum.xplain.xm.tasks.enums.TaskGranularityByFxCcyPairType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByInstrumentType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByRateType
import com.solum.xplain.xm.tasks.enums.TaskGranularityBySectorType
import com.solum.xplain.xm.tasks.enums.TaskGranularityByTradeType
import com.solum.xplain.xm.workflow.state.CurveConfigurationProviders
import com.solum.xplain.xm.workflow.state.InstrumentCurveConfigurations
import com.solum.xplain.xm.workflow.state.MdDashboardContext
import com.solum.xplain.xm.workflow.state.MdDashboardState
import com.solum.xplain.xm.workflow.state.MdKeyContext
import com.solum.xplain.xm.workflow.state.MdKeyState
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext
import com.solum.xplain.xm.workflow.state.MdPreliminaryState
import com.solum.xplain.xm.workflow.state.VdDashboardContext
import com.solum.xplain.xm.workflow.state.VdDashboardState
import com.solum.xplain.xm.workflow.state.VdPhaseContext
import com.solum.xplain.xm.workflow.state.VdPhaseState
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class XmProcessExecutionQueryRepositoryTest extends IntegrationSpecification {
  @Resource
  XmProcessExecutionQueryRepository repository
  @Resource
  ExceptionManagementResultMapper exceptionManagementResultMapper
  @Resource
  MongoOperations operations

  void setup() {
    def dash1Date = BitemporalDate.newOf(LocalDate.of(2022, 11, 29), LocalDateTime.now())
    def dash2Date = BitemporalDate.newOf(LocalDate.of(2022, 11, 30), LocalDateTime.now())
    def providers = new ProvidersVo("PRI", "SEC", null, null)

    def dashboardContext = new VdDashboardContext(VdExceptionManagementPortfolioFilter.empty(),
      [], dash1Date, UserBuilder.user())
    operations.insertAll([
      new ProcessExecution(processId: VD_XM_PROCESS_ID, businessKey: "dash1",
      status: WorkflowStatus.ACTIVE,
      currentState: new VdDashboardState(),
      context: dashboardContext,
      parentBusinessKey: "dash1-irs",
      rootBusinessKey: "dash1"
      ),
      new ProcessExecution(processId: VD_XM_PHASE_PROCESS_ID, businessKey: "irs", status: WorkflowStatus.ACTIVE,
      currentState: new VdPhaseState(),
      context: new VdPhaseContext(dash1Date, new IpvDataGroupVo("ipv1", "ipv1"), null, PricingSlot.LDN_1600, SlaDeadline.LDN_1600, new Trade(currency: "EUR", externalTradeId: "irs", productGroup: CoreProductTypeGroup.RATES, productType: CoreProductType.IRS), "EUR", providers, IpvExceptionManagementPhase.OVERLAY_1, null),
      parentBusinessKey: "dash1-irs",
      rootBusinessKey: "dash1"
      ),
      new ProcessExecution(processId: VD_XM_PHASE_PROCESS_ID, businessKey: "inf", status: WorkflowStatus.ACTIVE,
      currentState: new VdPhaseState(),
      context: new VdPhaseContext(dash1Date, new IpvDataGroupVo("ipv1", "ipv1"), null, PricingSlot.LDN_1600, SlaDeadline.LDN_1600, new Trade(currency: "USD", externalTradeId: "inf", productGroup: CoreProductTypeGroup.RATES, productType: CoreProductType.INFLATION), "USD", providers, IpvExceptionManagementPhase.OVERLAY_1, null),
      parentBusinessKey: "dash1-inf",
      rootBusinessKey: "dash1"
      ),
      new ProcessExecution(processId: VD_XM_PHASE_PROCESS_ID, businessKey: "fxo", status: WorkflowStatus.ACTIVE,
      currentState: new VdPhaseState(),
      context: new VdPhaseContext(dash1Date, new IpvDataGroupVo("ipv1", "ipv1"), null, PricingSlot.LDN_1600, SlaDeadline.LDN_1600, new Trade(currencyPair: "USD/NOK", externalTradeId: "fxo", productGroup: CoreProductTypeGroup.FX, productType: CoreProductType.FXOPT), "USD", providers, IpvExceptionManagementPhase.OVERLAY_1, null),
      parentBusinessKey: "dash1-fxo",
      rootBusinessKey: "dash1"
      ),
      new ProcessExecution(processId: VD_XM_PHASE_PROCESS_ID, businessKey: "fxf", status: WorkflowStatus.ACTIVE,
      currentState: new VdPhaseState(),
      context: new VdPhaseContext(dash1Date, new IpvDataGroupVo("ipv1", "ipv1"), null, PricingSlot.LDN_1600, SlaDeadline.LDN_1600, new Trade(currencyPair: "EUR/USD", externalTradeId: "fxf", productGroup: CoreProductTypeGroup.FX, productType: CoreProductType.FXFWD), "EUR", providers, IpvExceptionManagementPhase.OVERLAY_1, null),
      parentBusinessKey: "dash1-fxf",
      rootBusinessKey: "dash1"
      ),
      new ProcessExecution(processId: VD_XM_PHASE_PROCESS_ID, businessKey: "cds", status: WorkflowStatus.ACTIVE,
      currentState: new VdPhaseState(),
      context: new VdPhaseContext(dash1Date, new IpvDataGroupVo("ipv1", "ipv1"), null, PricingSlot.LDN_1600, SlaDeadline.LDN_1600, new Trade(creditSector: CreditSector.ENERGY, externalTradeId: "cds", productGroup: CoreProductTypeGroup.CREDIT, productType: CoreProductType.CDS), "USD", providers, IpvExceptionManagementPhase.OVERLAY_1, null),
      parentBusinessKey: "dash1-cds",
      rootBusinessKey: "dash1"
      ),
      new ProcessExecution(processId: VD_XM_PHASE_PROCESS_ID, businessKey: "cdx", status: WorkflowStatus.ACTIVE,
      currentState: new VdPhaseState(),
      context: new VdPhaseContext(dash1Date, new IpvDataGroupVo("ipv1", "ipv1"), null, PricingSlot.LDN_1600, SlaDeadline.LDN_1600, new Trade(creditSector: CreditSector.MUNICIPALITIES, externalTradeId: "cdx", productGroup: CoreProductTypeGroup.CREDIT, productType: CoreProductType.CREDIT_INDEX), "USD", providers, IpvExceptionManagementPhase.OVERLAY_1, null),
      parentBusinessKey: "dash1-cdx",
      rootBusinessKey: "dash1"
      ),
      new ProcessExecution(processId: VD_XM_PHASE_PROCESS_ID, businessKey: "other", status: WorkflowStatus.ACTIVE,
      currentState: new VdPhaseState(),
      context: new VdPhaseContext(dash1Date, new IpvDataGroupVo("ipv1", "ipv1"), null, PricingSlot.LDN_1600, SlaDeadline.LDN_1700, new Trade(externalTradeId: "other", productGroup: GenericProductGroup.CUSTOM_OTHER, productType: GenericProductType.CUSTOM_OTHER_1), "USD", providers, IpvExceptionManagementPhase.OVERLAY_1, null),
      parentBusinessKey: "dash1-other",
      rootBusinessKey: "dash1"
      ),
      new ProcessExecution(processId: VD_XM_PHASE_PROCESS_ID, businessKey: "ipv2", status: WorkflowStatus.ACTIVE,
      currentState: new VdPhaseState(),
      context: new VdPhaseContext(dash1Date, new IpvDataGroupVo("ipv2", "ipv2"), null, PricingSlot.LDN_1600, SlaDeadline.LDN_1600, new Trade(externalTradeId: "ipv2", productGroup: CoreProductTypeGroup.RATES, productType: CoreProductType.IRS), "USD", providers, IpvExceptionManagementPhase.OVERLAY_1, null),
      parentBusinessKey: "dash1-ipv2",
      rootBusinessKey: "dash1"
      ),
      new ProcessExecution(processId: VD_XM_PHASE_PROCESS_ID, businessKey: "dash2", status: WorkflowStatus.ACTIVE,
      currentState: new VdPhaseState(),
      context: new VdPhaseContext(dash2Date, new IpvDataGroupVo("ipv2", "ipv2"), null, PricingSlot.LDN_1600, SlaDeadline.LDN_1600, new Trade(currency: "USD", externalTradeId: "dash2", productGroup: CoreProductTypeGroup.RATES, productType: CoreProductType.IRS), "USD", providers, IpvExceptionManagementPhase.OVERLAY_1, null),
      parentBusinessKey: "dash2-x",
      rootBusinessKey: "dash2"
      )
    ])

    def curveConfigs = ["cc1", "cc2"].toSet()
    def mdProviders = ["PRI"]
    def instruments = [
      new InstrumentCurveConfigurations(IBOR_FIXING_DEPOSIT_EUR_6M, curveConfigs),
      new InstrumentCurveConfigurations(FIXED_IBOR_SWAP_EUR_6M, curveConfigs),
      new InstrumentCurveConfigurations(FIXED_OVERNIGHT_SWAP_EUR_EONIA_OIS, curveConfigs),
      new InstrumentCurveConfigurations(FRA_EUR_6M, curveConfigs),
      new InstrumentCurveConfigurations(IBOR_FUTURE_EUR_3M, curveConfigs),
      new InstrumentCurveConfigurations(OVERNIGHT_IBOR_BASIS_SWAP, curveConfigs),
      new InstrumentCurveConfigurations(IBOR_IBOR_SWAP, curveConfigs),
      new InstrumentCurveConfigurations(XCCY_IBOR_IBOR_SWAP_EUR_VS_USD, curveConfigs),
      new InstrumentCurveConfigurations(FIXED_INFLATION_SWAP_EUR_EXT_CPI, curveConfigs),
      new InstrumentCurveConfigurations(SWAPTION_ATM_EUR_6M_VOLS, curveConfigs),
      new InstrumentCurveConfigurations(SWAPTION_SKEW_USD_3M_VOLS, curveConfigs),
      new InstrumentCurveConfigurations(CAP_FLOOR_VOL_USD_3M_VOLS, curveConfigs),
      new InstrumentCurveConfigurations(FX_RATE_EUR_USD, curveConfigs),
      new InstrumentCurveConfigurations(FX_SWAP_EUR_USD, curveConfigs),
      new InstrumentCurveConfigurations(FX_VOL_EUR_USD, curveConfigs),
      new InstrumentCurveConfigurations(FX_VOL_SKEW_EUR_USD, curveConfigs),
      new InstrumentCurveConfigurations(CDS_1Y, curveConfigs)
    ]
    def parentProcess = operations.insert(new ProcessExecution<MdDashboardState, MdDashboardContext>(
      processId: MD_XM_PROCESS_ID,
      businessKey: "urn:dashboard:mdDash1",
      rootBusinessKey: "urn:dashboard:mdDash1",
      status: WorkflowStatus.ACTIVE,
      currentState: new MdDashboardState(),
      context: new MdDashboardContext("mdgId", "mdgName",[], dash1Date, null, [], UserBuilder.user())
      ))
    def keyExecutions = operations.insertAll(instruments.collect { inst ->
      new ProcessExecution<>(
        processId: MD_XM_KEY_PROCESS_ID,
        businessKey: "urn:dashboard:mdDash1-" + inst.instrumentDefinition().instrument.name(),
        status: WorkflowStatus.ACTIVE,
        currentState: new MdKeyState(),
        context: new MdKeyContext(dash1Date, null, "mdgId", "mdgName", inst.instrumentDefinition(),
        [], [], [], inst.curveConfigIds().collect {
          new CurveConfigurationProviders(it, it, it, new MarketDataProviders(primary: "PRI"))
        }),
        rootBusinessKey: parentProcess.rootBusinessKey,
        parentExecutionId: parentProcess.id
        )
    })
    def phaseExecutions = operations.insertAll(keyExecutions.collectMany { ProcessExecution<MdKeyState, MdKeyContext> ppe ->
      mdProviders.collect { provider ->
        new ProcessExecution<>(
          processId: MD_XM_PRELIMINARY_PROCESS_ID, businessKey: ppe.businessKey + "-" + provider, status: WorkflowStatus.ACTIVE,
          currentState: new MdPreliminaryState(),
          context: new MdPreliminaryContext(ppe.context.stateDate(), ppe.context.previousDate(), ppe.context.marketDataGroupId(),
          ppe.context.marketDataGroupName(), exceptionManagementResultMapper.fromDefinition(ppe.context.instrument()), ValueBidAskType.MID,
          provider, [], null, false),
          rootBusinessKey: ppe.rootBusinessKey,
          )
      }
    })
  }

  void cleanup() {
    operations.remove(new Query(), PROCESS_EXECUTION_COLLECTION)
  }

  // this tests task granularity only - the rest of the filtering is tested by
  // IpvExceptionManagementCalculationRepositoryTest."should return views from workflow filtered"
  def "should find IPV clearing task entries using granularity"() {
    given:
    def taskDef = Map.of(IpvExceptionManagementPhase.OVERLAY_1, new IpvTasksDefinition(
      granularityByTradeType: granularityByTradeType,
      granularityByRate: granularityByRate,
      granularityByFxCcyPairType: granularityByFxCcyPairType,
      granularityBySector: granularityBySector,
      granularityByContractualTerm: granularityByContractualTerm
      ))

    when:
    def result = repository.getIpvClearingViews(
      taskId,
      taskDef,
      false,
      false,
      false,
      new Criteria(),
      ScrollRequest.of(0, 10)
      )
    def keys = result.collect {it.id }

    then:
    keys.size() == expectedKeys.size()
    keys.containsAll(expectedKeys)

    where:
    taskId                                                        | granularityByTradeType                        | granularityByRate                   | granularityByFxCcyPairType                  | granularityBySector                 | granularityByContractualTerm                  || expectedKeys
    []                                                            | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["irs", "inf", "fxo", "fxf", "cds", "cdx", "other", "ipv2", "dash2"]
    [""]                                                          | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["irs", "inf", "fxo", "fxf", "cds", "cdx", "other", "ipv2", "dash2"]
    ["ipvDataGroupId=ipv1"]                                       | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["irs", "inf", "fxo", "fxf", "cds", "cdx", "other"]
    ["ipvDataGroupId=ipv1", "ipvDataGroupId=ipv2"]                 | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["irs", "inf", "fxo", "fxf", "cds", "cdx", "other", "ipv2", "dash2"]
    ["businessKey=dash1"]                                         | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["irs", "inf", "fxo", "fxf", "cds", "cdx", "other", "ipv2"]
    ["businessKey=dash1&ignored=RATES"]                           | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["irs", "inf", "fxo", "fxf", "cds", "cdx", "other", "ipv2"]
    ["valuationDate=2022-11-30T00:00:00.000Z"]                    | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["dash2"]
    ["ipvDataGroupId=ipv1&granularityByTradeType=RATES"]          | TaskGranularityByTradeType.TRADE_ASSET_CLASS  | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["irs", "inf"]
    ["ipvDataGroupId=ipv1&granularityByTradeType=IRS"]            | TaskGranularityByTradeType.TRADE_TYPE         | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["irs"]
    ["ipvDataGroupId=ipv1&granularityByRate=USD"]                 | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.CURRENCY  | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["inf"]
    ["ipvDataGroupId=ipv1"]                                       | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.CURRENCY  | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["fxo", "fxf", "cds", "cdx", "other"]
    ["ipvDataGroupId=ipv1&granularityByFxCcyPairType=USD"]        | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.DOMESTIC_CCY | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["fxo"]
    ["ipvDataGroupId=ipv1&granularityByFxCcyPairType=EUR/USD"]    | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.CCY_PAIR     | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["fxf"]
    ["ipvDataGroupId=ipv1"]                                       | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.CCY_PAIR     | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.NONE         || ["irs", "inf", "cds", "cdx", "other"]
    ["ipvDataGroupId=ipv1&granularityBySector=ENERGY"]            | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.SECTOR  | TaskGranularityByContractualTerm.NONE         || ["cds"]
    ["ipvDataGroupId=ipv1"]                                       | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.SECTOR  | TaskGranularityByContractualTerm.NONE         || ["irs", "inf", "fxo", "fxf", "other"]
    ["ipvDataGroupId=ipv1&granularityByContractualTerm=LDN_1700"] | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByContractualTerm.SLA_DEADLINE || ["other"]
    ["ipvDataGroupId=ipv1"]                                       | TaskGranularityByTradeType.NONE               | TaskGranularityByRateType.CURRENCY  | TaskGranularityByFxCcyPairType.CCY_PAIR     | TaskGranularityBySectorType.SECTOR  | TaskGranularityByContractualTerm.NONE         || ["other"]
  }

  // this tests task granularity only - the rest of the filtering is tested by
  // ExceptionManagementCalculationRepositoryTest."should return preliminary items filtered"
  def "should find preliminary clearing task entries using granularity"() {
    given:
    def taskDef = Map.of(TaskExceptionManagementType.PRELIMINARY, new TasksDefinition(
      granularityByAssetClassType: granularityByAssetClassType,
      granularityByRate: granularityByRate,
      granularityByFxCcyPair: granularityByFxCcyPair,
      granularityBySector: granularityBySector,
      granularityByInstrument: granularityByInstrument
      ))

    when:
    def result = repository.getPreliminaryClearingViews(taskId, taskDef, false, false, new Criteria(), ScrollRequest.of(0, 20))
    def keys = result.collect {it.id }

    then:
    keys.size() == expectedInstruments.size()
    keys.containsAll(expectedInstruments.collect { "urn:dashboard:mdDash1-" + it.instrument.name() + "-PRI" })

    where:
    taskId                                                                                                | granularityByAssetClassType               | granularityByRate                   | granularityByFxCcyPair                      | granularityBySector                 | granularityByInstrument                   || expectedInstruments
    ["taskExceptionManagementType=PRELIMINARY&businessKey=urn:dashboard:mdDash1"]                         | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || [
      IBOR_FIXING_DEPOSIT_EUR_6M,
      FIXED_IBOR_SWAP_EUR_6M,
      FIXED_OVERNIGHT_SWAP_EUR_EONIA_OIS,
      FRA_EUR_6M,
      IBOR_FUTURE_EUR_3M,
      OVERNIGHT_IBOR_BASIS_SWAP,
      IBOR_IBOR_SWAP,
      XCCY_IBOR_IBOR_SWAP_EUR_VS_USD,
      FIXED_INFLATION_SWAP_EUR_EXT_CPI,
      SWAPTION_ATM_EUR_6M_VOLS,
      SWAPTION_SKEW_USD_3M_VOLS,
      CAP_FLOOR_VOL_USD_3M_VOLS,
      FX_RATE_EUR_USD,
      FX_SWAP_EUR_USD,
      FX_VOL_EUR_USD,
      FX_VOL_SKEW_EUR_USD,
      CDS_1Y
    ]
    ["taskExceptionManagementType=PRELIMINARY&businessKey=urn:dashboard:mdDash1&ignored=RATES"]          | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || [
      IBOR_FIXING_DEPOSIT_EUR_6M,
      FIXED_IBOR_SWAP_EUR_6M,
      FIXED_OVERNIGHT_SWAP_EUR_EONIA_OIS,
      FRA_EUR_6M,
      IBOR_FUTURE_EUR_3M,
      OVERNIGHT_IBOR_BASIS_SWAP,
      IBOR_IBOR_SWAP,
      XCCY_IBOR_IBOR_SWAP_EUR_VS_USD,
      FIXED_INFLATION_SWAP_EUR_EXT_CPI,
      SWAPTION_ATM_EUR_6M_VOLS,
      SWAPTION_SKEW_USD_3M_VOLS,
      CAP_FLOOR_VOL_USD_3M_VOLS,
      FX_RATE_EUR_USD,
      FX_SWAP_EUR_USD,
      FX_VOL_EUR_USD,
      FX_VOL_SKEW_EUR_USD,
      CDS_1Y
    ]
    ["taskExceptionManagementType=PRELIMINARY&valuationDate=2022-11-30T00:00:00.000Z"]                    | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || []
    ["taskExceptionManagementType=PRELIMINARY&granularityByAssetClassType=CREDIT"]                        | TaskGranularityByAssetClassType.ASSET     | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || [CDS_1Y]
    ["taskExceptionManagementType=PRELIMINARY&granularityByAssetClassType=FX_RATES"]                      | TaskGranularityByAssetClassType.SUB_ASSET | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || [FX_RATE_EUR_USD]
    ["taskExceptionManagementType=PRELIMINARY&granularityByRate=USD"]                                     | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.CURRENCY  | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || [SWAPTION_SKEW_USD_3M_VOLS, CAP_FLOOR_VOL_USD_3M_VOLS]
    ["taskExceptionManagementType=PRELIMINARY"]                                                           | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.CURRENCY  | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || [
      FX_RATE_EUR_USD,
      FX_SWAP_EUR_USD,
      FX_VOL_EUR_USD,
      FX_VOL_SKEW_EUR_USD,
      CDS_1Y
    ]
    ["taskExceptionManagementType=PRELIMINARY&granularityByFxCcyPair=EUR"]                                | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.DOMESTIC_CCY | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || [FX_RATE_EUR_USD, FX_SWAP_EUR_USD, FX_VOL_EUR_USD, FX_VOL_SKEW_EUR_USD]
    ["taskExceptionManagementType=PRELIMINARY&granularityByFxCcyPair=EUR/USD"]                            | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.CCY_PAIR     | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || [FX_RATE_EUR_USD, FX_SWAP_EUR_USD, FX_VOL_EUR_USD, FX_VOL_SKEW_EUR_USD]
    ["taskExceptionManagementType=PRELIMINARY"]                                                           | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.CCY_PAIR     | TaskGranularityBySectorType.NONE    | TaskGranularityByInstrumentType.NONE      || [
      IBOR_FIXING_DEPOSIT_EUR_6M,
      FIXED_IBOR_SWAP_EUR_6M,
      FIXED_OVERNIGHT_SWAP_EUR_EONIA_OIS,
      FRA_EUR_6M,
      IBOR_FUTURE_EUR_3M,
      OVERNIGHT_IBOR_BASIS_SWAP,
      IBOR_IBOR_SWAP,
      XCCY_IBOR_IBOR_SWAP_EUR_VS_USD,
      FIXED_INFLATION_SWAP_EUR_EXT_CPI,
      SWAPTION_ATM_EUR_6M_VOLS,
      SWAPTION_SKEW_USD_3M_VOLS,
      CAP_FLOOR_VOL_USD_3M_VOLS,
      CDS_1Y
    ]
    ["taskExceptionManagementType=PRELIMINARY&granularityBySector=INDUSTRIALS"]                           | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.SECTOR  | TaskGranularityByInstrumentType.NONE      || [CDS_1Y]
    ["taskExceptionManagementType=PRELIMINARY"]                                                           | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.SECTOR  | TaskGranularityByInstrumentType.NONE      || [
      IBOR_FIXING_DEPOSIT_EUR_6M,
      FIXED_IBOR_SWAP_EUR_6M,
      FIXED_OVERNIGHT_SWAP_EUR_EONIA_OIS,
      FRA_EUR_6M,
      IBOR_FUTURE_EUR_3M,
      OVERNIGHT_IBOR_BASIS_SWAP,
      IBOR_IBOR_SWAP,
      XCCY_IBOR_IBOR_SWAP_EUR_VS_USD,
      FIXED_INFLATION_SWAP_EUR_EXT_CPI,
      SWAPTION_ATM_EUR_6M_VOLS,
      SWAPTION_SKEW_USD_3M_VOLS,
      CAP_FLOOR_VOL_USD_3M_VOLS,
      FX_RATE_EUR_USD,
      FX_SWAP_EUR_USD,
      FX_VOL_EUR_USD,
      FX_VOL_SKEW_EUR_USD
    ]
    ["taskExceptionManagementType=PRELIMINARY&granularityByInstrument=SWAPTION_ATM"]                      | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE   | TaskGranularityByInstrumentType.INSTRUMENT || [SWAPTION_ATM_EUR_6M_VOLS]
    ["taskExceptionManagementType=PRELIMINARY"]                                                           | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE   | TaskGranularityByInstrumentType.INSTRUMENT || [
      FX_RATE_EUR_USD,
      FX_SWAP_EUR_USD,
      FX_VOL_EUR_USD,
      FX_VOL_SKEW_EUR_USD,
      CDS_1Y
    ]
    ["taskExceptionManagementType=PRELIMINARY&granularityByInstrument=BOND_YIELD"]                        | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE   | TaskGranularityByInstrumentType.RATE_TYPE  || []
    ["taskExceptionManagementType=PRELIMINARY&granularityByInstrument=OTHER"]                             | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE   | TaskGranularityByInstrumentType.RATE_TYPE  || [
      IBOR_FIXING_DEPOSIT_EUR_6M,
      FIXED_IBOR_SWAP_EUR_6M,
      FIXED_OVERNIGHT_SWAP_EUR_EONIA_OIS,
      FRA_EUR_6M,
      IBOR_FUTURE_EUR_3M,
      OVERNIGHT_IBOR_BASIS_SWAP,
      IBOR_IBOR_SWAP,
      XCCY_IBOR_IBOR_SWAP_EUR_VS_USD
    ]
    ["taskExceptionManagementType=PRELIMINARY"]                                                           | TaskGranularityByAssetClassType.NONE      | TaskGranularityByRateType.NONE      | TaskGranularityByFxCcyPairType.NONE         | TaskGranularityBySectorType.NONE   | TaskGranularityByInstrumentType.RATE_TYPE  || [
      FIXED_INFLATION_SWAP_EUR_EXT_CPI,
      SWAPTION_ATM_EUR_6M_VOLS,
      SWAPTION_SKEW_USD_3M_VOLS,
      CAP_FLOOR_VOL_USD_3M_VOLS,
      FX_RATE_EUR_USD,
      FX_SWAP_EUR_USD,
      FX_VOL_EUR_USD,
      FX_VOL_SKEW_EUR_USD,
      CDS_1Y
    ]
  }

  def "should find dashboards by state date"() {
    when:
    def result = repository.findDashboardsByStateDate(LocalDate.of(2022, 11, 29))
    def keys = result.collect { it.businessKey }

    then:
    keys.size() == 1
    keys.containsAll(["dash1"])

    when:
    result = repository.findDashboardsByStateDate(LocalDate.of(2022, 11, 30))
    keys = result.collect { it.id }

    then:
    keys.size() == 0
  }

  def "should find preliminary clearing task entries from instrumentResultPreliminary"() {
    given:
    def taskDef = Map.of(TaskExceptionManagementType.PRELIMINARY, new TasksDefinition(
      granularityByAssetClassType: TaskGranularityByAssetClassType.NONE,
      granularityByRate: TaskGranularityByRateType.NONE,
      granularityByFxCcyPair: TaskGranularityByFxCcyPairType.NONE,
      granularityBySector: TaskGranularityBySectorType.NONE,
      granularityByInstrument: TaskGranularityByInstrumentType.NONE
      ))

    def unBrokenPrelim = new InstrumentResultPreliminary(dashboardId: "mdDash1", appliedTestsCount: 1, hasBreaks: false,
    instrument: new Instrument(
    assetClassGroup: CoreAssetGroup.FX,
    assetClass: CoreAssetClass.FX_RATES,
    fxPair: "EUR/JPY",
    instrumentType: FX_RATE,
    instrumentTypeSort: 4,
    key: "EUR/JPY",
    mdkName: "EUR/JPY SPOT",
    underlying: "EUR/JPY")
    )
    operations.insert(unBrokenPrelim)

    when:
    def result = repository.getPreliminaryClearingViews(["taskExceptionManagementType=PRELIMINARY&businessKey=urn:dashboard:mdDash1"],
    taskDef, false, false, new Criteria(), ScrollRequest.of(0, 20))
    def keys = result.collect { it.id }

    then:
    keys.size() == 18 // 17 from setup + 1 from prelim added in this test.
  }
}
