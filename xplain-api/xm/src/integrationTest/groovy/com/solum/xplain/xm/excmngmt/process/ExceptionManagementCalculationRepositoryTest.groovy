package com.solum.xplain.xm.excmngmt.process

import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.IR_RATE
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FRA
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.IBOR_FIXING_DEPOSIT
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.IBOR_FUTURE
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.MID
import static com.solum.xplain.shared.utils.filter.FilterOperation.EQUAL
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE_RANGE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_BATCH_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.MD_DASHBOARD_ID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.TRS_MARKET_DATA_DASHBOARD
import static com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider.ofBreakWithoutLevel
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.REJECTED
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.VERIFIED
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_APPROVAL
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_RESOLUTION
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_SUBMISSION
import static com.solum.xplain.xm.excmngmt.process.ExceptionManagementResultMapper.EXC_RESULT_MAPPER
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.APPROVED
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.BATCH_PROCESSING
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.IN_OVERLAY
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.IN_PRELIMINARY
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.PRELIMINARY_APPROVED
import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.PRELIMINARY_BATCH_APPROVED
import static com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType.KEEP
import static com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType.OVERRIDE_USER
import static com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType.PREVIOUS_DAY
import static com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType.SWITCH_TO_SECONDARY
import static com.solum.xplain.xm.workflow.MdXmWorkflowProvider.MD_XM_OVERLAY_PROCESS_ID
import static com.solum.xplain.xm.workflow.MdXmWorkflowProvider.MD_XM_PRELIMINARY_PROCESS_ID
import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN
import static java.math.BigDecimal.ZERO
import static java.time.temporal.ChronoUnit.SECONDS
import static org.springframework.data.mongodb.core.query.Criteria.where

import com.solum.xplain.core.classifiers.xm.CoreThresholdLevel
import com.solum.xplain.core.common.AuditContext
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinitionBuilder
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.extensions.enums.CreditSector
import com.solum.xplain.shared.utils.filter.FilterOperation
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.value.WorkflowStatus
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.entity.DashboardBuilder
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup
import com.solum.xplain.xm.dashboards.entity.TrsMdExceptionManagementSetup
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.enums.VerificationStatus
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence
import com.solum.xplain.xm.excmngmt.form.ApplyVerificationForm
import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm
import com.solum.xplain.xm.excmngmt.form.VerificationForm
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult
import com.solum.xplain.xm.excmngmt.process.data.Instrument
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultOverlay
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultResolution
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import com.solum.xplain.xm.excmngmt.process.form.ApplyTaskResolutionForm
import com.solum.xplain.xm.excmngmt.process.form.InstrumentFilterForm
import com.solum.xplain.xm.excmngmt.process.form.ResolutionForm
import com.solum.xplain.xm.excmngmt.process.form.TaskInstrumentResultBreaksFilterForm
import com.solum.xplain.xm.excmngmt.process.value.LegalEntityTrsDataProviderResolver
import com.solum.xplain.xm.excmngmt.process.view.InstrumentOverlayResultView
import com.solum.xplain.xm.excmngmt.process.view.InstrumentPreliminaryResultView
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter
import com.solum.xplain.xm.excmngmt.rules.value.AssetFilterForm
import com.solum.xplain.xm.excmngmt.value.CountedFilter
import com.solum.xplain.xm.excmngmt.value.EntryResultStatusHistory
import com.solum.xplain.xm.excmngmt.value.RevertEntriesRequest
import com.solum.xplain.xm.tasks.entity.TaskExecution
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus
import com.solum.xplain.xm.workflow.state.MdOverlayContext
import com.solum.xplain.xm.workflow.state.MdOverlayState
import com.solum.xplain.xm.workflow.state.MdPreliminaryContext
import com.solum.xplain.xm.workflow.state.MdPreliminaryState
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.bson.types.ObjectId
import org.springframework.beans.MutablePropertyValues
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ExceptionManagementCalculationRepositoryTest extends IntegrationSpecification {

  @Resource
  ExceptionManagementCalculationRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  private AuditContext audit() {
    return new AuditContext(AuditUser.of(user), LocalDateTime.now().truncatedTo(SECONDS))
  }

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), ExceptionManagementResult)
    operations.remove(new Query(), InstrumentResultPreliminary)
    operations.remove(new Query(), InstrumentResultOverlay)
    operations.remove(new Query(), Dashboard)
    operations.remove(new Query(), ProcessExecution)
  }

  def "should insert preliminary"() {
    setup:
    def result = ExceptionManagementResult.preliminary(
    "id",
    LocalDate.of(2020, 1, 1),
    MdExceptionManagementSetup.newOf(
    EntityReference.newOf("marketDataGroupId", "name"),
    [CurveConfigurationInstrumentResolver.empty()],
    LocalDate.of(2020, 1, 1),
    false),
    TrsMdExceptionManagementSetup.newOf(
    EntityReference.newOf("marketDataGroupId2", "name2"),
    [LegalEntityTrsDataProviderResolver.empty()],
    LocalDate.of(2020, 1, 1),
    ))

    def preliminaries = [new InstrumentResultPreliminary()]

    when:
    def entityId = repository.insertPreliminary(result, preliminaries)

    then:
    def loaded = operations.findOne(new Query(where("id").is(entityId.id)), ExceptionManagementResult.class)
    loaded.getCurveDate() == LocalDate.of(2020, 1, 1)
    loaded.getMarketDataGroupId() == "marketDataGroupId"
    loaded.getMarketDataGroupName() == "name"
    loaded.getTrsMarketDataGroupId() == "marketDataGroupId2"
    loaded.getTrsMarketDataGroupName() == "name2"

    def preliminary = operations.find(new Query(where("exceptionManagementResultId").is(loaded.getId())),
    InstrumentResultPreliminary.class)

    preliminary.size() == 1
    preliminary[0].getExceptionManagementResultId() == loaded.getId()
  }

  def "should insert overlay"() {
    setup:
    def result = ExceptionManagementResult.preliminary(
    "id",
    LocalDate.of(2020, 1, 1),
    MdExceptionManagementSetup.newOf(
    EntityReference.newOf("marketDataGroupId", "name"),
    [CurveConfigurationInstrumentResolver.empty()],
    LocalDate.of(2020, 1, 1),
    false),
    TrsMdExceptionManagementSetup.newOf(
    EntityReference.newOf("marketDataGroupId2", "name2"),
    [LegalEntityTrsDataProviderResolver.empty()],
    LocalDate.of(2020, 1, 1),
    ))

    operations.insert(result)

    def overlays = [new InstrumentResultOverlay()]

    when:
    def entityId = repository.insertOverlay(result, overlays)

    then:
    def loaded = operations.findOne(new Query(where("id").is(entityId.id)), ExceptionManagementResult.class)
    loaded.getCurveDate() == LocalDate.of(2020, 1, 1)
    loaded.getMarketDataGroupId() == "marketDataGroupId"
    loaded.getMarketDataGroupName() == "name"

    def o = operations.find(new Query(where("exceptionManagementResultId").is(loaded.getId())),
    InstrumentResultOverlay.class)

    o.size() == 1
    o[0].getExceptionManagementResultId() == loaded.getId()
  }

  def "should return entity"() {
    setup:
    def result = new ExceptionManagementResult(curveDate: LocalDate.of(2020, 1, 1))
    operations.insert(result)

    when:
    def entity = repository.entity(result.id)

    then:
    entity.isRight()
    def loaded = entity.getOrNull()
    loaded.getCurveDate() == LocalDate.of(2020, 1, 1)
  }

  def "should correctly apply resolution"() {
    def result = new ExceptionManagementResult(status: IN_PRELIMINARY)
    operations.insert(result)

    def p1 = new InstrumentResultPreliminary(
    taskId: "id",
    exceptionManagementResultId: result.id,
    providerData: new ProviderData(provider: "1"),
    status: WAITING_RESOLUTION,
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE, key: "KEY"),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new InstrumentResultPreliminary(
    taskId: "id",
    exceptionManagementResultId: result.id,
    providerData: new ProviderData(provider: "1"),
    status: REJECTED,
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p3 = new InstrumentResultPreliminary(
    taskId: "id",
    exceptionManagementResultId: result.id,
    providerData: new ProviderData(provider: "2"),
    status: VERIFIED,
    resolution: InstrumentResultResolution.newOf(),
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    operations.insertAll([p1, p2, p3])

    def form = new ApplyTaskResolutionForm(
    List.of("id"),
    new InstrumentFilterForm([], ["1"], new AssetFilterForm([], [], [], [], []), [], [], [], []),
    new ResolutionForm(OVERRIDE_USER, TEN, "comment"),
    null)

    when:
    def r = repository.saveResult(audit(), form, IN_PRELIMINARY, WAITING_APPROVAL, new ExceptionManagementEvidence("FILEID", "FILENAME"))

    then:
    r.isRight()
    def all = operations.find(new Query(where("taskId").is("id")), InstrumentResultPreliminary.class)
    .sort(d -> d.getId())
    all.size() == 3
    all[0].status == WAITING_APPROVAL
    all[0].resolution.resolution == OVERRIDE_USER
    all[0].resolution.resolutionComment == "comment"
    all[0].resolution.value == TEN
    all[0].resolution.provider == null
    all[0].resolution.approvalComment == null
    all[0].resolution.resolutionEvidence == new ExceptionManagementEvidence("FILEID", "FILENAME")
    all[0].previousStatuses.size() == 1
    all[0].previousStatuses[0].status == WAITING_RESOLUTION
    all[0].previousStatuses[0].modifiedBy == AuditUser.of(user)
    all[0].previousStatuses[0].modifiedAt != null

    all[1].status == WAITING_APPROVAL
    all[1].resolution.resolution == OVERRIDE_USER
    all[1].resolution.resolutionComment == "comment"
    all[1].resolution.resolutionEvidence == new ExceptionManagementEvidence("FILEID", "FILENAME")

    all[2].status == VERIFIED
    all[2].resolution == p3.resolution
    all[2].resolution.resolutionEvidence == null
  }

  def "should correctly verify items overall"() {
    def result = new ExceptionManagementResult(status: IN_PRELIMINARY)
    operations.insert(result)

    def p1 = new InstrumentResultPreliminary(
    taskId: "id",
    exceptionManagementResultId: result.id,
    providerData: new ProviderData(provider: "1"),
    status: WAITING_APPROVAL,
    resolution: InstrumentResultResolution.newOf(),
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE, key: "KEY"),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new InstrumentResultPreliminary(
    taskId: "id",
    exceptionManagementResultId: result.id,
    providerData: new ProviderData(provider: "1"),
    status: WAITING_APPROVAL,
    resolution: InstrumentResultResolution.newOf(),
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])

    operations.insertAll([p1, p2])

    def forms = new ApplyVerificationForm(
    [],
    new VerificationForm(VerificationStatus.VERIFIED, "approvalComment"),
    ["id"]
    )

    when:
    def r = repository.verifyItems(forms, IN_PRELIMINARY, new ExceptionManagementEvidence())

    then:
    r.isRight()
    def all = operations.find(new Query(where("taskId").is("id")), InstrumentResultPreliminary.class)
    all.size() == 2

    all[0].status == WAITING_SUBMISSION
    all[0].resolution.resolution == null
    all[0].resolution.resolutionComment == null
    all[0].resolution.value == null
    all[0].resolution.provider == null
    all[0].resolution.approvalComment == "approvalComment"
    all[0].resolution.approvalEvidence == new ExceptionManagementEvidence()

    all[1].status == WAITING_SUBMISSION
    all[1].resolution.resolution == null
    all[1].resolution.resolutionComment == null
    all[1].resolution.value == null
    all[1].resolution.provider == null
    all[1].resolution.approvalComment == "approvalComment"
  }

  def "should correctly verify items overall and selected ids"() {
    def result = new ExceptionManagementResult(status: IN_PRELIMINARY)
    operations.insert(result)

    def p1 = new InstrumentResultPreliminary(
    taskId: "id",
    exceptionManagementResultId: result.id,
    providerData: new ProviderData(provider: "1"),
    status: WAITING_APPROVAL,
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE, key: "KEY"),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new InstrumentResultPreliminary(
    taskId: "id",
    exceptionManagementResultId: result.id,
    providerData: new ProviderData(provider: "1"),
    status: WAITING_APPROVAL,
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])

    operations.insertAll([p1, p2])

    def forms = new ApplyVerificationForm(
    [p1.id],
    new VerificationForm(VerificationStatus.VERIFIED, "comment"),
    ["id"]
    )

    when:
    def r = repository.verifyItems(forms, IN_PRELIMINARY, new ExceptionManagementEvidence())

    then:
    r.isRight()
    def all = operations.find(new Query(where("taskId").is("id")), InstrumentResultPreliminary.class)
    all.size() == 2
    all[0].status == WAITING_SUBMISSION
    all[0].id == p1.id
    all[1].status == WAITING_APPROVAL
    all[1].id == p2.id
  }

  def "should correctly verify overlay items overall and selected ids"() {
    def result = new ExceptionManagementResult(status: IN_OVERLAY)
    operations.insert(result)

    def p1 = new InstrumentResultOverlay(
    taskId: "id",
    exceptionManagementResultId: result.id,
    primaryProviderData: new ProviderData(provider: "1"),
    status: WAITING_APPROVAL,
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE, key: "KEY"),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new InstrumentResultOverlay(
    taskId: "id",
    exceptionManagementResultId: result.id,
    primaryProviderData: new ProviderData(provider: "1"),
    status: WAITING_APPROVAL,
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])

    operations.insertAll([p1, p2])

    def forms = new ApplyVerificationForm(
    [p1.id],
    new VerificationForm(VerificationStatus.VERIFIED, "comment"),
    ["id"]
    )

    when:
    def r = repository.verifyItems(forms, IN_OVERLAY, new ExceptionManagementEvidence())

    then:
    r.isRight()
    def all = operations.find(new Query(where("taskId").is("id")), InstrumentResultOverlay.class)
    all.size() == 2
    all[0].status == WAITING_SUBMISSION
    all[0].id == p1.id
    all[1].status == WAITING_APPROVAL
    all[1].id == p2.id
  }

  def "should fail to verify items with an unexpected status"() {
    setup:
    def forms = new ApplyVerificationForm(["anyId"], new VerificationForm(VerificationStatus.VERIFIED, "comment"), ["anyId"])
    when:
    repository.verifyItems(forms, APPROVED, new ExceptionManagementEvidence())
    then:
    thrown IllegalStateException
  }

  def "should correctly rollback preliminary entries"() {
    def p1 = new InstrumentResultPreliminary(
    taskId: "id",
    dashboardId: "dashboardId",
    providerData: new ProviderData(provider: "1"),
    status: WAITING_SUBMISSION,
    resolution: new InstrumentResultResolution(
    resolution: SWITCH_TO_SECONDARY,
    resolutionComment: null,
    value: null,
    provider: null,
    approvalComment: null,
    ),
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE, key: "KEY"),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new InstrumentResultPreliminary(
    taskId: "id",
    dashboardId: "dashboardId",
    providerData: new ProviderData(provider: "1"),
    status: REJECTED,
    resolution: InstrumentResultResolution.newOf(),
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p3 = new InstrumentResultPreliminary(
    taskId: "id",
    dashboardId: "dashboardId",
    providerData: new ProviderData(provider: "2"),
    status: WAITING_APPROVAL,
    resolution: InstrumentResultResolution.newOf(),
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p4 = new InstrumentResultPreliminary(
    taskId: "id",
    dashboardId: "dashboardId",
    providerData: new ProviderData(provider: "2"),
    status: WAITING_RESOLUTION,
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    operations.insertAll([p1, p2, p3, p4])

    when:
    def r = repository.revertPreliminaryItems(RevertEntriesRequest.revertResolutionTask(new TaskExecution(dashboardId: "dashboardId", id: "id")))

    then:
    r.isRight()
    def reverted = operations.find(new Query(where("taskId").is("id")), InstrumentResultPreliminary.class)
    .sort(d -> d.getId())

    reverted.size() == 4
    reverted[0].status == WAITING_SUBMISSION
    reverted[1].status == REJECTED

    reverted[2].status == WAITING_RESOLUTION
    reverted[2].previousStatuses[0].status == WAITING_APPROVAL

    reverted[3].status == WAITING_RESOLUTION
    reverted[3].previousStatuses.isEmpty()

    when:
    r = repository.revertPreliminaryItems(RevertEntriesRequest.revertVerificationTask(new TaskExecution(dashboardId: "dashboardId", id: "id")))

    then:
    r.isRight()
    def allReverted = operations.find(new Query(where("taskId").is("id")), InstrumentResultPreliminary.class)
    .sort(d -> d.getId())

    allReverted.size() == 4
    allReverted[0].status == WAITING_APPROVAL
    allReverted[0].previousStatuses[0].status == WAITING_SUBMISSION

    allReverted[1].status == WAITING_APPROVAL
    allReverted[1].previousStatuses[0].status == REJECTED

    allReverted[2].status == WAITING_RESOLUTION
    allReverted[2].previousStatuses[0].status == WAITING_APPROVAL

    allReverted[3].status == WAITING_RESOLUTION
    allReverted[3].previousStatuses.isEmpty()
  }

  def "should correctly rollback overlay entries"() {
    def p1 = new InstrumentResultOverlay(
    taskId: "id",
    dashboardId: "dashboardId",
    status: WAITING_SUBMISSION,
    resolution: new InstrumentResultResolution(
    resolution: SWITCH_TO_SECONDARY,
    resolutionComment: null,
    value: null,
    provider: null,
    approvalComment: null,
    ),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new InstrumentResultOverlay(
    taskId: "id",
    dashboardId: "dashboardId",
    status: REJECTED,
    resolution: InstrumentResultResolution.newOf(),
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p3 = new InstrumentResultOverlay(
    taskId: "id",
    dashboardId: "dashboardId",
    status: WAITING_APPROVAL,
    resolution: InstrumentResultResolution.newOf(),
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p4 = new InstrumentResultOverlay(
    taskId: "id",
    dashboardId: "dashboardId",
    status: WAITING_RESOLUTION,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    operations.insertAll([p1, p2, p3, p4])

    when:
    def r = repository.revertOverlayItems(RevertEntriesRequest.revertResolutionTask(new TaskExecution(dashboardId: "dashboardId", id: "id")))

    then:
    r.isRight()
    def reverted = operations.find(new Query(where("taskId").is("id")), InstrumentResultOverlay.class)
    .sort(d -> d.getId())

    reverted.size() == 4
    reverted[0].status == WAITING_SUBMISSION
    reverted[1].status == REJECTED

    reverted[2].status == WAITING_RESOLUTION
    reverted[2].previousStatuses[0].status == WAITING_APPROVAL

    reverted[3].status == WAITING_RESOLUTION
    reverted[3].previousStatuses.isEmpty()

    when:
    r = repository.revertOverlayItems(RevertEntriesRequest.revertVerificationTask(new TaskExecution(dashboardId: "dashboardId", id: "id")))

    then:
    r.isRight()
    def allReverted = operations.find(new Query(where("taskId").is("id")), InstrumentResultOverlay.class)
    .sort(d -> d.getId())

    allReverted.size() == 4
    allReverted[0].status == WAITING_APPROVAL
    allReverted[0].previousStatuses[0].status == WAITING_SUBMISSION

    allReverted[1].status == WAITING_APPROVAL
    allReverted[1].previousStatuses[0].status == REJECTED

    allReverted[2].status == WAITING_RESOLUTION
    allReverted[2].previousStatuses[0].status == WAITING_APPROVAL

    allReverted[3].status == WAITING_RESOLUTION
    allReverted[3].previousStatuses.isEmpty()
  }

  def "should fail to apply resolution when resolved value is null"() {
    def result = new ExceptionManagementResult(status: IN_PRELIMINARY)
    operations.insert(result)

    def p1 = new InstrumentResultPreliminary(
    taskId: "id",
    status: WAITING_RESOLUTION,
    hasBreaks: true,
    providerData: new ProviderData(provider: "1", previousValue: TEN),
    instrument: new Instrument(assetClass: IR_RATE, key: "KEY1"),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new InstrumentResultPreliminary(
    taskId: "id",
    status: WAITING_RESOLUTION,
    hasBreaks: true,
    providerData: new ProviderData(provider: "1"),
    instrument: new Instrument(assetClass: IR_RATE, key: "KEY2"),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    operations.insertAll([p1, p2])

    def form = new ApplyTaskResolutionForm(
    ["id"],
    new InstrumentFilterForm([], ["1"], new AssetFilterForm([], [], [], [], []), [], [], [], []),
    new ResolutionForm(PREVIOUS_DAY, TEN, "comment"),
    null)

    when:
    def r = repository.saveResult(audit(), form, IN_PRELIMINARY, VERIFIED, new ExceptionManagementEvidence())

    then:
    r.isLeft()
    r.left().getOrNull().description == "Resolved value is empty for instrument KEY2"
  }

  def "should correctly undo resolution"() {
    def result = new ExceptionManagementResult(status: IN_PRELIMINARY)
    operations.insert(result)

    def p1 = new InstrumentResultPreliminary(
    taskId: "id",
    exceptionManagementResultId: result.id,
    providerData: new ProviderData(provider: "1"),
    status: WAITING_APPROVAL,
    resolution: InstrumentResultResolution.newOf(),
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE, key: "KEY"),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new InstrumentResultPreliminary(
    taskId: "id",
    exceptionManagementResultId: result.id,
    providerData: new ProviderData(provider: "1"),
    status: VERIFIED,
    resolution: InstrumentResultResolution.newOf(),
    hasBreaks: true,
    instrument: new Instrument(assetClass: IR_RATE),
    breakTests: [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    operations.insertAll([p1, p2])

    when:
    def r = repository.undoResolution([p1.id], ["id"], IN_PRELIMINARY)

    then:
    r.isRight()
    def all = operations.find(new Query(where("taskId").is("id")),
    InstrumentResultPreliminary.class)
    all.size() == 2
    all[0].status == WAITING_RESOLUTION
    all[0].resolution == null
    all[0].previousStatuses[0].status == WAITING_APPROVAL

    all[1].status == VERIFIED
    all[1].previousStatuses.isEmpty()
  }

  def "should correctly approve to status IN_PRELIMINARY when all items verified"() {
    def result = new ExceptionManagementResult(status: IN_PRELIMINARY)
    operations.insert(result)

    def p = new InstrumentResultPreliminary(
    exceptionManagementResultId: result.id,
    status: VERIFIED,
    resolution: new InstrumentResultResolution(
    resolution: null,
    resolutionComment: null,
    value: TEN,
    provider: null,
    approvalComment: null,
    ),
    providerData: ProviderData.of("BBG", ONE, ONE, BID),
    instrument: new Instrument(key: "key")
    )
    operations.insertAll([p])

    when:
    def r = repository.approvePreliminary(result.id)

    then:
    r.isRight()
    def all = operations.find(new Query(where("id").is(result.getId())), ExceptionManagementResult.class)
    all.size() == 1
    all[0].status == PRELIMINARY_APPROVED
  }

  def "should correctly approve to status APPROVED when all items verified"() {
    def result = new ExceptionManagementResult(
    status: IN_OVERLAY,
    curveDate: LocalDate.of(2020, 1, 1),
    marketDataGroupId: new ObjectId().toHexString()
    )
    operations.insert(result)
    def p = new InstrumentResultOverlay(
    exceptionManagementResultId: result.id,
    resolution: new InstrumentResultResolution(
    resolution: null,
    resolutionComment: null,
    value: TEN,
    provider: null,
    approvalComment: null,
    ),
    status: VERIFIED,
    instrument: new Instrument(key: "KEY"))
    operations.insertAll([p])

    when:
    def r = repository.approveOverlay(result.id)

    then:
    r.isRight()
    def all = operations.find(new Query(where("id").is(result.getId())), ExceptionManagementResult.class)
    all.size() == 1
    all[0].status == APPROVED
  }

  def "should check if no preliminary items with statuses are present"() {
    setup:

    def p1 = new InstrumentResultPreliminary(
    exceptionManagementResultId: "id",
    taskId: "id",
    status: WAITING_RESOLUTION
    )
    def p2 = new InstrumentResultPreliminary(
    exceptionManagementResultId: "id",
    taskId: "id",
    status: REJECTED
    )
    def p3 = new InstrumentResultPreliminary(
    exceptionManagementResultId: "id",
    taskId: "id",
    status: VERIFIED
    )
    def p4 = new InstrumentResultPreliminary(
    exceptionManagementResultId: "id",
    taskId: "id",
    status: WAITING_APPROVAL
    )
    operations.insertAll([p1, p2, p3, p4])
    expect:
    !repository.noPreliminaryItemsWithStatus(["id"], ["id"], WAITING_APPROVAL)
    !repository.noPreliminaryItemsWithStatus(["id"], ["id"], WAITING_APPROVAL, REJECTED)
    !repository.noPreliminaryItemsWithStatus(["id"], ["id"], WAITING_RESOLUTION, REJECTED)
  }

  def "should mark preliminary entries for submission as verified"() {
    setup:

    def p1 = new InstrumentResultOverlay(
    dashboardId: "dashboardId",
    taskId: "id",
    status: WAITING_SUBMISSION
    )
    def p2 = new InstrumentResultOverlay(
    dashboardId: "dashboardId",
    taskId: "id",
    status: VERIFIED
    )
    def p3 = new InstrumentResultOverlay(
    dashboardId: "dashboardId",
    taskId: "id2",
    status: WAITING_SUBMISSION
    )
    operations.insertAll([p1, p2, p3])
    when:
    repository.markOverlayItemsAsVerified("dashboardId", "id")

    then:
    def all = operations.findAll(InstrumentResultOverlay.class)
    all[0].status == VERIFIED
    all[1].status == VERIFIED
    all[2].status == WAITING_SUBMISSION
  }

  def "should mark overlay entries for submission as verified"() {
    setup:

    def p1 = new InstrumentResultPreliminary(
    dashboardId: "dashboardId",
    taskId: "id",
    status: WAITING_SUBMISSION
    )
    def p2 = new InstrumentResultPreliminary(
    dashboardId: "dashboardId",
    taskId: "id",
    status: VERIFIED
    )
    def p3 = new InstrumentResultPreliminary(
    dashboardId: "dashboardId",
    taskId: "id2",
    status: WAITING_SUBMISSION
    )
    operations.insertAll([p1, p2, p3])
    when:
    repository.markPreliminaryItemsAsVerified("dashboardId", "id")

    then:
    def all = operations.findAll(InstrumentResultPreliminary.class)
    all[0].status == VERIFIED
    all[1].status == VERIFIED
    all[2].status == WAITING_SUBMISSION
  }

  def "should check if no overlay items are present"() {
    setup:

    def p1 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    taskId: "id",
    status: WAITING_RESOLUTION
    )
    def p2 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    taskId: "id",
    status: REJECTED
    )
    def p3 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    taskId: "id",
    status: VERIFIED
    )
    def p4 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    taskId: "id",
    status: WAITING_APPROVAL
    )
    operations.insertAll([p1, p2, p3, p4])
    expect:
    !repository.noOverlayItemsWithStatus(["id"], ["id"], WAITING_APPROVAL)
    !repository.noOverlayItemsWithStatus(["id"], ["id"], WAITING_APPROVAL, REJECTED)
    !repository.noOverlayItemsWithStatus(["id"], ["id"], WAITING_RESOLUTION, REJECTED)
  }

  def "should restrict approve to status IN_PRELIMINARY when not all items verified"() {
    def result = new ExceptionManagementResult(status: IN_PRELIMINARY)
    operations.insert(result)

    def p = new InstrumentResultPreliminary(exceptionManagementResultId: result.id, status: WAITING_RESOLUTION)
    operations.insertAll([p])

    when:
    def r = repository.approvePreliminary(result.id)

    then:
    r.isRight()
  }

  def "should return current calculation"() {
    setup:
    def rApproved = new ExceptionManagementResult(
    curveDate: LocalDate.of(2020, 1, 1),
    marketDataGroupId: "MDID",
    trsMarketDataGroupId: "TRSMDID",
    status: APPROVED)
    def rActive = new ExceptionManagementResult(
    curveDate: LocalDate.of(2020, 1, 2),
    marketDataGroupId: "MDID",
    trsMarketDataGroupId: "TRSMDID",
    status: IN_PRELIMINARY)
    operations.insertAll([rApproved, rActive])

    when:
    def res = repository.calculationByDateAndMarketData(LocalDate.of(2020, 1, 2), mdId, trsMdId)

    then:
    res.isPresent() == exists

    where:
    mdId      | trsMdId   | exists
    null      | "TRSMDID" | true
    null      | "MDID"    | false
    "MDID"    | null      | true
    "TRSMDID" | null      | false
    "MDID"    | "TRSMDID" | true
  }

  def "should return preliminary items filtered"() {
    setup:
    def breaksWithoutAndWithTriggered = [
      new InstrumentResultBreak(breakTestName: "Test1", providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(breakTestName: "Test2", providerValue: new EntryResultBreakByProvider(triggered: false))
    ]

    def breaksWithAllTriggered = [
      new InstrumentResultBreak(breakTestName: "Test1", providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(breakTestName: "Test2", providerValue: new EntryResultBreakByProvider(triggered: true))
    ]


    def taskId = ObjectId.get().toString()
    def taskExecution = new TaskExecution(id: taskId, status: TaskExecutionStatus.IN_APPROVAL)
    def resultWithoutBreaks = new InstrumentResultPreliminary(taskId: taskId, breakTests: [], hasBreaks: false)
    def resolvedResultWithBreaks = new InstrumentResultPreliminary(taskId: taskId, hasBreaks: true, breakTests: breaksWithoutAndWithTriggered, status: WAITING_APPROVAL)
    def rejectedResultWithBreaks = new InstrumentResultPreliminary(taskId: taskId, hasBreaks: true, breakTests: breaksWithoutAndWithTriggered, status: REJECTED)
    def verifiedResultWithAllBreaks = new InstrumentResultPreliminary(
    taskId: taskId,
    breakTests: breaksWithAllTriggered,
    status: VERIFIED,
    hasBreaks: true,
    resolution: new InstrumentResultResolution(
    resolution: SWITCH_TO_SECONDARY,
    resolutionComment: null,
    value: null,
    provider: null,
    approvalComment: null,
    ))
    def pendingResultWithBreaks = new InstrumentResultPreliminary(taskId: taskId, hasBreaks: true, breakTests: breaksWithoutAndWithTriggered, status: WAITING_RESOLUTION)

    operations.insertAll([
      taskExecution,
      resultWithoutBreaks,
      resolvedResultWithBreaks,
      verifiedResultWithAllBreaks,
      rejectedResultWithBreaks,
      pendingResultWithBreaks
    ]
    )

    def result
    when: 'only results with breaks'
    result = repository.preliminaryItemsDaily(
    displayFilterForm(true),
    List.of(taskId),
    emptyTableFilter(),
    ScrollRequest.of(0, 10, Sort.by(InstrumentPreliminaryResultView.Fields.id))
    )

    then:
    result.content.size() == 4

    result.content[0].id == resolvedResultWithBreaks.id
    result.content[0].breakTests.size() == 2
    result.content[0].stepStatus == WorkflowStatus.ACTIVE

    result.content[1].id == verifiedResultWithAllBreaks.id
    result.content[1].breakTests.size() == 2
    result.content[1].stepStatus == null

    result.content[2].id == rejectedResultWithBreaks.id
    result.content[2].breakTests.size() == 2
    result.content[2].stepStatus == WorkflowStatus.FINALIZING

    result.content[3].id == pendingResultWithBreaks.id
    result.content[3].breakTests.size() == 2
  }

  def "should return workflow preliminary items filtered"() {
    setup:
    def breaksWithoutAndWithTriggered = [
      new InstrumentResultBreak(breakTestName: "Test1", providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(breakTestName: "Test2", providerValue: new EntryResultBreakByProvider(triggered: false))
    ]

    def breaksWithAllTriggered = [
      new InstrumentResultBreak(breakTestName: "Test1", providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(breakTestName: "Test2", providerValue: new EntryResultBreakByProvider(triggered: true))
    ]

    def resultWithoutBreaks = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key1",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(null, null, null, null, null, null, null, null, [], false),
    currentState: new MdPreliminaryState()
    )
    def resolvedResultWithBreaks = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key2",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(null, null, null, null, null, null, null, null, breaksWithoutAndWithTriggered, true),
    currentState: new MdPreliminaryState(entryStatus: WAITING_APPROVAL)
    )
    def rejectedResultWithBreaks = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key4",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(null, null, null, null, null, null, null, null, breaksWithoutAndWithTriggered, true),
    currentState: new MdPreliminaryState(entryStatus: REJECTED)
    )
    def verifiedResultWithAllBreaks = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key3",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(null, null, null, null, null, null, null, null, breaksWithAllTriggered, true),
    currentState: new MdPreliminaryState(entryStatus: VERIFIED,
    resolution: SWITCH_TO_SECONDARY, resolutionComment: null, providerName: null, manualResolutionValue: null, approvalComment: null)
    )
    def pendingResultWithBreaks = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key5",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(null, null, null, null, null, null, null, null, breaksWithoutAndWithTriggered, true),
    currentState: new MdPreliminaryState(entryStatus: WAITING_RESOLUTION)
    )

    operations.insertAll([
      resultWithoutBreaks,
      resolvedResultWithBreaks,
      verifiedResultWithAllBreaks,
      rejectedResultWithBreaks,
      pendingResultWithBreaks
    ]
    )

    operations.insert(new StepInstance(
    executionId: pendingResultWithBreaks.id,
    status: WorkflowStatus.FINALIZING,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdPreliminaryState(),
    outcome: new MutablePropertyValues().add(MdPreliminaryState.Fields.resolution, KEEP)
    ))

    def result
    when: 'only results with breaks'
    result = repository.preliminaryItemsDaily(
    displayFilterForm(true),
    List.of("id&stepId=decideResolution&taskExceptionManagementType=PRELIMINARY"),
    emptyTableFilter(),
    ScrollRequest.of(0, 10, Sort.by(InstrumentPreliminaryResultView.Fields.id))
    )

    then:
    result.content.size() == 4

    result.content[0].id == resolvedResultWithBreaks.businessKey
    result.content[0].breakTests.size() == 2

    result.content[1].id == verifiedResultWithAllBreaks.businessKey
    result.content[1].breakTests.size() == 2

    result.content[2].id == rejectedResultWithBreaks.businessKey
    result.content[2].breakTests.size() == 2

    result.content[3].id == pendingResultWithBreaks.businessKey
    result.content[3].breakTests.size() == 2

    and: "FINALIZING step overrides status and resolution"
    result.content[3].stepStatus == WorkflowStatus.FINALIZING
    result.content[3].resolution.resolution == KEEP
    result.content[3].status == WAITING_APPROVAL
  }

  def "should return preliminary items all"() {
    setup:
    def iInstrumentsWithoutAndWithTriggered = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak()
    ]

    def iInstrumentsWithAllTriggered = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))
    ]


    def taskId = ObjectId.get().toString()
    def taskExecution = new TaskExecution(id: taskId, status: TaskExecutionStatus.IN_RESOLUTION)
    def pWithoutBreaks = new InstrumentResultPreliminary(taskId: taskId, breakTests: [])
    def pWithBreaks = new InstrumentResultPreliminary(taskId: taskId, breakTests: iInstrumentsWithoutAndWithTriggered)
    def pWithAllBreaks = new InstrumentResultPreliminary(taskId: taskId, breakTests: iInstrumentsWithAllTriggered)
    operations.insertAll([taskExecution, pWithoutBreaks, pWithBreaks, pWithAllBreaks])

    when:
    def result = repository.preliminaryItemsDaily(
    displayFilterForm(),
    List.of(taskId),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 3
    result.content[1].id == pWithBreaks.id
    result.content[1].breakTests.size() == 2
    result.content[2].id == pWithAllBreaks.id
    result.content[2].breakTests.size() == 2
  }

  def "should return workflow preliminary items all"() {
    setup:
    def dashDate = BitemporalDate.newOfNow()
    def iInstrumentsWithoutAndWithTriggered = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak()
    ]
    def iInstrumentsWithAllTriggered = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))
    ]

    def pWithoutBreaks = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key1",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(dashDate, null, "mdgId", "mgdName", new Instrument(), MID, "PRI", [], [], false),
    currentState: new MdPreliminaryState()
    )
    def pWithBreaks = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key2",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(dashDate, null, "mdgId", "mgdName", new Instrument(), MID, "PRI", [], iInstrumentsWithoutAndWithTriggered, true),
    currentState: new MdPreliminaryState()
    )
    def pWithAllBreaks = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key3",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(dashDate, null, "mdgId", "mgdName", new Instrument(), MID, "PRI", [], iInstrumentsWithAllTriggered, true),
    currentState: new MdPreliminaryState()
    )
    operations.insertAll([pWithoutBreaks, pWithBreaks, pWithAllBreaks])

    when:
    def result = repository.preliminaryItemsDaily(
    displayFilterForm(),
    List.of("id&taskExceptionManagementType=PRELIMINARY"),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 3
    result.content[1].id == pWithBreaks.businessKey
    result.content[1].breakTests.size() == 2
    result.content[2].id == pWithAllBreaks.businessKey
    result.content[2].breakTests.size() == 2
  }

  def "should return preliminary items default sorted"() {
    setup:
    def taskId = ObjectId.get().toString()
    def taskExecution = new TaskExecution(id: taskId, status: TaskExecutionStatus.IN_APPROVAL)
    def p1 = new InstrumentResultPreliminary(
    taskId: taskId,
    allProvidersData: [
      new ProviderData(provider: "1", value: 1, bidAskType: BID),
      new ProviderData(provider: "1", value: 2, bidAskType: ASK),
      new ProviderData(provider: "2", value: 3, bidAskType: BID),
    ],
    providerData: new ProviderData(provider: "1", value: 1, bidAskType: BID),
    instrument: new Instrument(assetClass: IR_RATE, instrumentType: CoreInstrumentType.TERM_DEPOSIT, instrumentTypeSort: 1, parsedTenor: 1),
    breakTests: [
      new InstrumentResultBreak(threshold: ONE, providerValue: new EntryResultBreakByProvider(triggeredThreshold: ZERO, triggeredThresholdLevel: 1))
    ],
    previousStatuses: [
      EntryResultStatusHistory.newOf(audit().user(), LocalDateTime.now(), VERIFIED, "resolution", "approval")
    ],
    maxTriggeredThresholdLevel: 1,
    )

    def p2 = new InstrumentResultPreliminary(
    taskId: taskId,
    instrument: new Instrument(assetClass: IR_RATE, instrumentType: CoreInstrumentType.TERM_DEPOSIT, instrumentTypeSort: 2, parsedTenor: 1),
    breakTests: [new InstrumentResultBreak(threshold: ONE)])

    def p3 = new InstrumentResultPreliminary(
    taskId: taskId,
    instrument: new Instrument(assetClass: IR_RATE, instrumentType: CoreInstrumentType.FIXED_IBOR_SWAP, instrumentTypeSort: 1, parsedTenor: 2),
    breakTests: [
      new InstrumentResultBreak(threshold: TEN, providerValue: new EntryResultBreakByProvider(triggeredThresholdLevel: 1)),
      new InstrumentResultBreak(threshold: TEN, providerValue: new EntryResultBreakByProvider(triggeredThresholdLevel: 2)),
    ],
    maxTriggeredThresholdLevel: 2,
    )

    operations.insertAll([taskExecution, p1, p2, p3])

    when:
    def result = repository.preliminaryItemsDaily(
    displayFilterForm(),
    List.of(taskId),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 3
    result.content[0].id == p1.id
    result.content[0].bidAskType == BID
    result.content[0].allProvidersData.size() == 2
    result.content[0].allProvidersData["1"].getBidValue() == 1
    result.content[0].allProvidersData["1"].getAskValue() == 2
    result.content[0].allProvidersData["2"].getBidValue() == 3
    result.content[0].allProvidersData["2"].getAskValue() == null
    result.content[0].previousStatuses.size() == 1
    result.content[0].previousStatuses[0].status == VERIFIED
    result.content[0].previousStatuses[0].modifiedBy == audit().user()
    result.content[0].previousStatuses[0].modifiedAt != null
    result.content[0].previousStatuses[0].resolutionComment == "resolution"
    result.content[0].previousStatuses[0].approvalComment == "approval"
    result.content[0].breakTests[0].threshold == ZERO
    result.content[0].breakTests[0].thresholdLevel == 1
    result.content[0].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_1
    result.content[1].id == p3.id
    result.content[1].breakTests[0].threshold == TEN
    result.content[1].breakTests[0].thresholdLevel == 1
    result.content[1].breakTests[1].threshold == TEN
    result.content[1].breakTests[1].thresholdLevel == 2
    result.content[1].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_2
    result.content[2].id == p2.id
    result.content[2].breakTests[0].threshold == ONE
    result.content[2].breakTests[0].thresholdLevel == null
    result.content[2].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_NULL
  }

  def "should return workflow preliminary items default sorted"() {
    setup:
    def p1 = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key1",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(null, null, null,null,new Instrument(instrumentTypeSort: 1, parsedTenor: 1), BID, "1",[
      new ProviderData(provider: "1", value: 1, bidAskType: BID),
      new ProviderData(provider: "1", value: 2, bidAskType: ASK),
      new ProviderData(provider: "2", value: 3, bidAskType: BID),
    ], [
      new InstrumentResultBreak(threshold: ONE, providerValue: new EntryResultBreakByProvider(triggeredThreshold: ZERO, triggeredThresholdLevel: 1))
    ], true),
    currentState: new MdPreliminaryState()
    )
    def p2 = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key2",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(null, null, null, null, new Instrument(instrumentTypeSort: 2, parsedTenor: 1), BID, "1",[], [new InstrumentResultBreak(threshold: ONE)], true),
    currentState: new MdPreliminaryState()
    )
    def p3 = new ProcessExecution<MdPreliminaryState, MdPreliminaryContext>(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    businessKey: "key3",
    rootBusinessKey: "id",
    context: new MdPreliminaryContext(null, null, null, null,new Instrument(instrumentTypeSort: 1, parsedTenor: 2), BID, "1",[], [
      new InstrumentResultBreak(threshold: TEN, providerValue: new EntryResultBreakByProvider(triggeredThresholdLevel: 1)),
      new InstrumentResultBreak(threshold: TEN, providerValue: new EntryResultBreakByProvider(triggeredThresholdLevel: 2)),
    ], true),
    currentState: new MdPreliminaryState()
    )
    operations.insertAll([p1, p2, p3])

    when:
    def result = repository.preliminaryItemsDaily(
    displayFilterForm(),
    List.of("id&taskExceptionManagementType=PRELIMINARY"),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 3
    result.content[0].id == p1.businessKey
    result.content[0].bidAskType == BID
    result.content[0].allProvidersData.size() == 2
    result.content[0].allProvidersData["1"].getBidValue() == 1
    result.content[0].allProvidersData["1"].getAskValue() == 2
    result.content[0].allProvidersData["2"].getBidValue() == 3
    result.content[0].allProvidersData["2"].getAskValue() == null
    result.content[0].breakTests[0].threshold == ZERO
    result.content[0].breakTests[0].thresholdLevel == 1
    result.content[0].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_1
    result.content[1].id == p3.businessKey
    result.content[1].bidAskType == BID
    result.content[1].breakTests[0].threshold == TEN
    result.content[1].breakTests[0].thresholdLevel == 1
    result.content[1].breakTests[1].threshold == TEN
    result.content[1].breakTests[1].thresholdLevel == 2
    result.content[1].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_2
    result.content[2].id == p2.businessKey
    result.content[2].bidAskType == BID
    result.content[2].breakTests[0].threshold == ONE
    result.content[2].breakTests[0].thresholdLevel == null
    result.content[2].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_NULL
  }

  def "should return overlay items filtered"() {
    setup:
    def dashboard = DashboardBuilder.MARKET_DATA_DASHBOARD
    operations.insert(dashboard)

    def breaksWithoutAndWithTriggered = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: false))
    ]

    def breaksWithAllTriggered = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))
    ]

    def taskId = ObjectId.get().toString()
    def taskExecution = new TaskExecution(id: taskId, status: TaskExecutionStatus.IN_APPROVAL)
    def resultWithoutBreaks = new InstrumentResultOverlay(
    dashboardId: dashboard.id,
    taskId: taskId, hasBreaks: false, breakTests: [])
    def resolvedResultWithBreaks = new InstrumentResultOverlay(
    dashboardId: dashboard.id,
    taskId: taskId, hasBreaks: true, breakTests: breaksWithoutAndWithTriggered, status: WAITING_APPROVAL)
    def rejectedResultWithBreaks = new InstrumentResultOverlay(
    dashboardId: dashboard.id,
    taskId: taskId, hasBreaks: true, breakTests: breaksWithoutAndWithTriggered, status: REJECTED)
    def verifiedResultWithAllBreaks = new InstrumentResultOverlay(
    dashboardId: dashboard.id,
    taskId: taskId,
    breakTests: breaksWithAllTriggered,
    status: VERIFIED,
    hasBreaks: true,
    resolution: new InstrumentResultResolution(
    resolution: SWITCH_TO_SECONDARY,
    resolutionComment: null,
    value: null,
    provider: null,
    approvalComment: null,
    ))
    def pendingResultWithBreaks = new InstrumentResultOverlay(
    dashboardId: dashboard.id,
    taskId: taskId, hasBreaks: true, breakTests: breaksWithoutAndWithTriggered, status: WAITING_RESOLUTION)

    operations.insertAll([
      taskExecution,
      resultWithoutBreaks,
      resolvedResultWithBreaks,
      verifiedResultWithAllBreaks,
      rejectedResultWithBreaks,
      pendingResultWithBreaks
    ]
    )

    def result
    when: 'only results with breaks'
    result = repository.overlayItems(
    List.of(taskId),
    displayFilterForm(true),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 4

    result.content[0].id == resolvedResultWithBreaks.id
    result.content[0].breakTests.size() == 2

    result.content[1].id == verifiedResultWithAllBreaks.id
    result.content[1].breakTests.size() == 2

    result.content[2].id == rejectedResultWithBreaks.id
    result.content[2].breakTests.size() == 2

    result.content[3].id == pendingResultWithBreaks.id
    result.content[3].breakTests.size() == 2
  }

  def "should return workflow overlay items filtered"() {
    setup:
    def breaksWithoutAndWithTriggered = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: false))
    ]

    def breaksWithAllTriggered = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))
    ]

    def resultWithoutBreaks = new ProcessExecution<MdOverlayState, MdOverlayContext>(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    businessKey: "key1",
    rootBusinessKey: "id",
    context: new MdOverlayContext(null, null, null, null, null, null, null, null, null, null, null, null, null, null, [:]),
    currentState: new MdOverlayState(breakTestResults: [], hasBreak: false)
    )
    def resolvedResultWithBreaks = new ProcessExecution<MdOverlayState, MdOverlayContext>(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    businessKey: "key2",
    rootBusinessKey: "id",
    context: new MdOverlayContext(null, null, null, null, null, null, null, null, null, null, null, null, null, null, [:]),
    currentState: new MdOverlayState(breakTestResults: breaksWithoutAndWithTriggered, hasBreak: true, entryStatus: WAITING_APPROVAL)
    )
    def rejectedResultWithBreaks = new ProcessExecution<MdOverlayState, MdOverlayContext>(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    businessKey: "key4",
    rootBusinessKey: "id",
    context: new MdOverlayContext(null, null, null, null, null, null, null, null, null, null, null, null, null, null, [:]),
    currentState: new MdOverlayState(breakTestResults: breaksWithoutAndWithTriggered, hasBreak: true, entryStatus: REJECTED)
    )
    def verifiedResultWithAllBreaks = new ProcessExecution<MdOverlayState, MdOverlayContext>(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    businessKey: "key3",
    rootBusinessKey: "id",
    context: new MdOverlayContext(null, null, null, null, null, null, null, null, null, null, null, null, null, null, [:]),
    currentState: new MdOverlayState(breakTestResults: breaksWithAllTriggered, hasBreak: true, entryStatus: VERIFIED,
    resolution: SWITCH_TO_SECONDARY, resolutionComment: null, providerName: null, manualResolutionValue: null, approvalComment: null)
    )
    def pendingResultWithBreaks = new ProcessExecution<MdOverlayState, MdOverlayContext>(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    businessKey: "key5",
    rootBusinessKey: "id",
    context: new MdOverlayContext(null, null, null, null, null, null, null, null, null, null, null, null, null, null, [:]),
    currentState: new MdOverlayState(breakTestResults: breaksWithoutAndWithTriggered, hasBreak: true, entryStatus: WAITING_RESOLUTION)
    )

    operations.insertAll([
      resultWithoutBreaks,
      resolvedResultWithBreaks,
      verifiedResultWithAllBreaks,
      rejectedResultWithBreaks,
      pendingResultWithBreaks
    ]
    )

    def result
    when: 'only results with breaks'
    result = repository.overlayItems(
    List.of("id&taskExceptionManagementType=OVERLAY"),
    displayFilterForm(true),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 4

    result.content[0].id == resolvedResultWithBreaks.businessKey
    result.content[0].breakTests.size() == 2

    result.content[1].id == verifiedResultWithAllBreaks.businessKey
    result.content[1].breakTests.size() == 2

    result.content[2].id == rejectedResultWithBreaks.businessKey
    result.content[2].breakTests.size() == 2

    result.content[3].id == pendingResultWithBreaks.businessKey
    result.content[3].breakTests.size() == 2
  }

  def "should return overlay items default sorted"() {
    setup:
    def dashboard = DashboardBuilder.MD_AND_TRS_MARKET_DATA_DASHBOARD
    operations.insert(dashboard)

    def pi = [new InstrumentResultBreak()]

    def taskId = ObjectId.get().toString()
    def taskExecution = new TaskExecution(id: taskId, status: TaskExecutionStatus.IN_APPROVAL)
    def p1 = new InstrumentResultOverlay(
    dashboardId: dashboard.id,
    curveConfigurationId: DashboardBuilder.CURVE_CONFIGURATION.entityId,
    valuationDate: LocalDate.of(2020, 1, 2),
    taskId: taskId,
    instrument: new Instrument(assetClass: IR_RATE, instrumentType: CoreInstrumentType.TERM_DEPOSIT, instrumentTypeSort: 1, parsedTenor: 1),
    primaryProviderData: new ProviderData(provider: "P1", bidAskType: BID, value: 1),
    allProvidersData: [
      new ProviderData(provider: "P1", bidAskType: BID, value: 1),
      new ProviderData(provider: "P1", bidAskType: ASK, value: 2),
      new ProviderData(provider: "P2", bidAskType: BID, value: 3),
    ],
    breakTests: pi,
    maxTriggeredThresholdLevel: 1,
    previousStatuses: [
      EntryResultStatusHistory.newOf(audit().user(), LocalDateTime.now(), VERIFIED, "resolution", "approval")
    ])

    def p2 = new InstrumentResultOverlay(
    curveConfigurationId: DashboardBuilder.CURVE_CONFIGURATION.entityId,
    dashboardId: dashboard.id,
    taskId: taskId,
    instrument: new Instrument(assetClass: IR_RATE, instrumentType: CoreInstrumentType.TERM_DEPOSIT, instrumentTypeSort: 2, parsedTenor: 1),
    maxTriggeredThresholdLevel: 2,
    breakTests: pi)

    def p3 = new InstrumentResultOverlay(
    companyId: DashboardBuilder.LEGAL_ENTITY_DATA_PROVIDER_SETTINGS.companyId,
    legalEntityId: DashboardBuilder.LEGAL_ENTITY_DATA_PROVIDER_SETTINGS.legalEntityId,
    dashboardId: dashboard.id,
    taskId: taskId,
    instrument: new Instrument(assetClass: IR_RATE, instrumentType: CoreInstrumentType.TERM_DEPOSIT, instrumentTypeSort: 1, parsedTenor: 2),
    breakTests: pi)

    operations.insertAll([taskExecution, p1, p2, p3])

    when:
    def result = repository.overlayItems(
    List.of(taskId),
    displayFilterForm(),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 3
    result.content[0].id == p1.id
    result.content[0].valuationDate == LocalDate.of(2020, 1, 2)
    result.content[0].bidAskType == BID
    result.content[0].primaryProviderData.provider == "P1"
    result.content[0].primaryProviderData.bidValue == 1
    result.content[0].primaryProviderData.askValue == 2
    result.content[0].previousStatuses.size() == 1
    result.content[0].previousStatuses[0].status == VERIFIED
    result.content[0].previousStatuses[0].modifiedBy == audit().user()
    result.content[0].previousStatuses[0].modifiedAt != null
    result.content[0].previousStatuses[0].resolutionComment == "resolution"
    result.content[0].previousStatuses[0].approvalComment == "approval"
    result.content[0].curveConfigurationId == DashboardBuilder.CURVE_CONFIGURATION.entityId
    result.content[0].curveConfigurationName == "CURVE CONFIGURATION NAME"
    result.content[1].id == p3.id
    result.content[1].companyId == "companyId"
    result.content[1].companyExternalId == "EXT_COMPANY"
    result.content[1].legalEntityId == "legalEntityId"
    result.content[1].legalEntityExternalId == "EXT_ENTITY"
    result.content[2].id == p2.id
    result.content[2].curveConfigurationId == DashboardBuilder.CURVE_CONFIGURATION.entityId
    result.content[2].curveConfigurationName == "CURVE CONFIGURATION NAME"

    when: 'only filtered max break level'
    def form = displayFilterForm()
    result = repository.overlayItems(
    List.of(taskId),
    form,
    new TableFilter([
      new SimpleFilterClause(InstrumentOverlayResultView.Fields.maxTriggeredThresholdLevel, EQUAL, "LEVEL_1")
    ]),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 1
    result.content[0].id == p1.id
    result.content[0].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_1
  }

  def "should return workflow overlay items default sorted"() {
    setup:
    def stateDate = BitemporalDate.newOf(LocalDate.of(2020, 1, 2))
    def p1 = new ProcessExecution<MdOverlayState, MdOverlayContext>(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    businessKey: "key1",
    rootBusinessKey: "id",
    context: new MdOverlayContext(stateDate, null, null, new Instrument(assetClass: IR_RATE, instrumentType: CoreInstrumentType.TERM_DEPOSIT, instrumentTypeSort: 1, parsedTenor: 1), BID, "ccId", "ccName", "cgId", new MarketDataProviders(primary: "1", secondary: "2"), [
      new ProviderData(provider: "1", value: 1, bidAskType: BID),
      new ProviderData(provider: "1", value: 2, bidAskType: ASK),
      new ProviderData(provider: "2", value: 3, bidAskType: BID),
    ], [
      new ProviderData(provider: "1", value: 4, bidAskType: BID),
      new ProviderData(provider: "1", value: 2, bidAskType: ASK),
      new ProviderData(provider: "2", value: 3, bidAskType: BID),
    ], [], null, null, [:]),
    currentState: new MdOverlayState(breakTestResults: [
      new InstrumentResultBreak(threshold: ONE, providerValue: new EntryResultBreakByProvider(triggeredThreshold: ZERO, triggeredThresholdLevel: 1))
    ])
    )
    def p2 = new ProcessExecution<MdOverlayState, MdOverlayContext>(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    businessKey: "key2",
    rootBusinessKey: "id",
    context: new MdOverlayContext(stateDate, null, null, new Instrument(assetClass: IR_RATE, instrumentType: CoreInstrumentType.TERM_DEPOSIT, instrumentTypeSort: 2, parsedTenor: 1), BID, "ccId", "ccName", "cgId", new MarketDataProviders(primary: "1", secondary: "2"), [], [], [], null, null, [:]),
    currentState: new MdOverlayState(breakTestResults: [new InstrumentResultBreak(threshold: ONE)])
    )
    def p3 = new ProcessExecution<MdOverlayState, MdOverlayContext>(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    businessKey: "key3",
    rootBusinessKey: "id",
    context: new MdOverlayContext(stateDate, null, null, new Instrument(assetClass: IR_RATE, instrumentType: CoreInstrumentType.TERM_DEPOSIT, instrumentTypeSort: 1, parsedTenor: 2), BID, "ccId", "ccName", "cgId", new MarketDataProviders(primary: "1", secondary: "2"), [], [], [], null, null, [:]),
    currentState: new MdOverlayState(breakTestResults: [
      new InstrumentResultBreak(threshold: TEN, providerValue: new EntryResultBreakByProvider(triggeredThresholdLevel: 1)),
      new InstrumentResultBreak(threshold: TEN, providerValue: new EntryResultBreakByProvider(triggeredThresholdLevel: 2)),
    ])
    )
    operations.insertAll([p1, p2, p3])

    when:
    def result = repository.overlayItems(
    List.of("id&taskExceptionManagementType=OVERLAY"),
    displayFilterForm(),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 3
    result.content[0].id == p1.businessKey
    result.content[0].valuationDate == LocalDate.of(2020, 1, 2)
    result.content[0].primaryProviderData.provider == "1"
    result.content[0].primaryProviderData.getBidValue() == 4
    result.content[0].primaryProviderData.getAskValue() == 2
    result.content[0].secondaryProviderData.provider == "2"
    result.content[0].secondaryProviderData.getBidValue() == 3
    result.content[0].secondaryProviderData.getAskValue() == null
    result.content[0].breakTests[0].threshold == ZERO
    result.content[0].breakTests[0].thresholdLevel == 1
    result.content[0].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_1
    result.content[1].id == p3.businessKey
    result.content[1].breakTests[0].threshold == TEN
    result.content[1].breakTests[0].thresholdLevel == 1
    result.content[1].breakTests[1].threshold == TEN
    result.content[1].breakTests[1].thresholdLevel == 2
    result.content[1].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_2
    result.content[2].id == p2.businessKey
    result.content[2].breakTests[0].threshold == ONE
    result.content[2].breakTests[0].thresholdLevel == null
    result.content[2].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_NULL
  }

  def "should return overlay breaks count"() {
    setup:
    def pi = [new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))]

    def p1 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p2 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p3 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi)
    def p4 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: VERIFIED)
    def p5 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 0, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: VERIFIED)
    def p6 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: WAITING_SUBMISSION)

    operations.insertAll([p1, p2, p3, p4, p5, p6])

    when:
    def result = repository.overlayBreaksCount(breaksFilterForm(), List.of("id"))

    then:
    result.totalTestsCount == 6
    result.appliedTestsCount == 5
    result.totalCount == 6
    result.breaksCount == 5
    result.verifiedCount == 2
    result.resolvedCount == 2
  }

  def "should return preliminary breaks count"() {
    setup:
    def pi = [new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))]

    def p1 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p2 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p3 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi)
    def p4 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: VERIFIED)
    def p5 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 0, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: VERIFIED)

    operations.insertAll([p1, p2, p3, p4, p5])

    when:
    def result = repository.preliminaryBreakCount(breaksFilterForm(), List.of("id"))

    then:
    result.totalTestsCount == 5
    result.appliedTestsCount == 4
    result.totalCount == 5
    result.breaksCount == 4
    result.verifiedCount == 1
    result.resolvedCount == 1
  }


  def "should return overlay breaks count - awaiting a resolution"() {
    setup:
    def pi = [new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))]

    def p1 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi, status: WAITING_RESOLUTION)
    def p2 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi,status: WAITING_RESOLUTION)
    def p3 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: WAITING_RESOLUTION)
    def p4 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: WAITING_RESOLUTION)
    def p5 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 0, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: WAITING_RESOLUTION)
    def p6 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: WAITING_RESOLUTION)

    operations.insertAll([p1, p2, p3, p4, p5, p6])

    when:
    def result = repository.overlayBreaksCount(breaksFilterForm(), List.of("id"))

    then:
    result.totalTestsCount == 6
    result.appliedTestsCount == 5
    result.totalCount == 6
    result.breaksCount == 5
    result.verifiedCount == 0
    result.resolvedCount == 0
    result.finalizingCount == 0
  }

  def "should return overlay breaks count - apply a resolution"() {
    setup:
    def pi = [new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))]

    def p1 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi,status: WAITING_APPROVAL)
    def p2 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi,status: WAITING_APPROVAL)
    def p3 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: WAITING_APPROVAL)
    def p4 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: WAITING_APPROVAL)
    def p5 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 0, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: WAITING_APPROVAL)
    def p6 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: WAITING_APPROVAL)

    operations.insertAll([p1, p2, p3, p4, p5, p6])

    when:
    def result = repository.overlayBreaksCount(breaksFilterForm(), List.of("id"))

    then:
    result.totalTestsCount == 6
    result.appliedTestsCount == 5
    result.totalCount == 6
    result.breaksCount == 5
    result.verifiedCount == 0
    result.resolvedCount == 5
    result.finalizingCount == 5
  }

  def "should return overlay breaks count - approve resolutions"() {
    setup:
    def pi = [new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))]

    def p1 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi,status: VERIFIED)
    def p2 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi,status: VERIFIED)
    def p3 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: VERIFIED)
    def p4 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: VERIFIED)
    def p5 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 0, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: VERIFIED)
    def p6 = new InstrumentResultOverlay(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: VERIFIED)

    operations.insertAll([p1, p2, p3, p4, p5, p6])

    when:
    def result = repository.overlayBreaksCount(breaksFilterForm(), List.of("id"))

    then:
    result.totalTestsCount == 6
    result.appliedTestsCount == 5
    result.totalCount == 6
    result.breaksCount == 5
    result.verifiedCount == 5
    result.resolvedCount == 5
    result.finalizingCount == 0
  }

  def "should return preliminary breaks count - awaiting a resolution"() {
    setup:
    def pi = [new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))]

    def p1 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p2 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p3 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi)
    def p4 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: WAITING_RESOLUTION)
    def p5 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 0, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi,status: WAITING_RESOLUTION)

    operations.insertAll([p1, p2, p3, p4, p5])

    when:
    def result = repository.preliminaryBreakCount(breaksFilterForm(), List.of("id"))

    then:
    result.totalTestsCount == 5
    result.appliedTestsCount == 4
    result.totalCount == 5
    result.breaksCount == 4
    result.verifiedCount == 0
    result.resolvedCount == 0
    result.finalizingCount == 0
  }
  def "should return preliminary breaks count - apply a resolution"() {
    setup:
    def pi = [new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))]

    def p1 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p2 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p3 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi)
    def p4 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: WAITING_APPROVAL)
    def p5 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 0, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: WAITING_APPROVAL)

    operations.insertAll([p1, p2, p3, p4, p5])

    when:
    def result = repository.preliminaryBreakCount(breaksFilterForm(), List.of("id"))

    then:
    result.totalTestsCount == 5
    result.appliedTestsCount == 4
    result.totalCount == 5
    result.breaksCount == 4
    result.verifiedCount == 0
    result.resolvedCount == 1
    result.finalizingCount == 1
  }
  def "should return preliminary breaks count - approve resolutions"() {
    setup:
    def pi = [new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true))]

    def p1 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p2 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    def p3 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi)
    def p4 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: true, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: VERIFIED)
    def p5 = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 0, instrument: new Instrument(instrumentType: IBOR_FUTURE), breakTests: pi, status: VERIFIED)

    operations.insertAll([p1, p2, p3, p4, p5])

    when:
    def result = repository.preliminaryBreakCount(breaksFilterForm(), List.of("id"))

    then:
    result.totalTestsCount == 5
    result.appliedTestsCount == 4
    result.totalCount == 5
    result.breaksCount == 4
    result.verifiedCount == 1
    result.resolvedCount == 1
    result.finalizingCount == 0
  }

  def "should return workflow overlay breaks count"() {
    setup:
    def  pi = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true, value: ONE))
    ]
    def piNullValue = [
      new InstrumentResultBreak(threshold: ONE, providerValue: new EntryResultBreakByProvider(triggered: true))
    ]
    def piSkipped = [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(false, null))]
    def stateDate = BitemporalDate.newOfNow()

    def context = new MdOverlayContext(
    stateDate,
    "mdgId",
    "mdgName",
    EXC_RESULT_MAPPER.fromDefinition(InstrumentDefinitionBuilder.CDS_1Y),
    MID,
    "ccId",
    "ccName",
    "cgId",
    null,
    [],
    [],
    [],
    new InstrumentResultResolution(),
    null, [:])

    def p1 = new ProcessExecution(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdOverlayState(
    entryStatus: WAITING_RESOLUTION,
    breakTestResults: pi,
    hasBreak: true
    )
    )
    def p2 = new ProcessExecution(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdOverlayState(
    entryStatus: WAITING_RESOLUTION,
    breakTestResults: pi,
    hasBreak: true
    )
    )
    def p3 = new ProcessExecution(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdOverlayState(
    entryStatus: WAITING_APPROVAL,
    breakTestResults: pi,
    hasBreak: true
    )
    )
    def p4 = new ProcessExecution(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdOverlayState(
    entryStatus: VERIFIED,
    breakTestResults: piNullValue,
    hasBreak: true
    )
    )
    def p5 = new ProcessExecution(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdOverlayState(
    entryStatus: VERIFIED,
    breakTestResults: piSkipped,
    )
    )
    def p6 = new ProcessExecution(
    processId: MD_XM_OVERLAY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdOverlayState(
    entryStatus: WAITING_RESOLUTION,
    breakTestResults: pi,
    hasBreak: true
    )
    )

    operations.insertAll([p1, p2, p3, p4, p5, p6])
    def s1 = new StepInstance(
    executionId: p1.id,
    status: WorkflowStatus.ACTIVE,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdOverlayState()
    )
    def s2 = new StepInstance(
    executionId: p2.id,
    status: WorkflowStatus.FINALIZING,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdOverlayState()
    )
    def s3 = new StepInstance(
    executionId: p3.id,
    status: WorkflowStatus.DONE,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdOverlayState()
    )
    def s4 = new StepInstance(
    executionId: p4.id,
    status: WorkflowStatus.DONE,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdOverlayState()
    )
    def s6 = new StepInstance(
    executionId: p6.id,
    status: WorkflowStatus.HELD,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdOverlayState()
    )
    operations.insertAll([s1, s2, s3, s4, s6])

    when:
    def result = repository.overlayBreaksCount(breaksFilterForm(), ["id&stepId=decideResolution&taskExceptionManagementType=OVERLAY"])

    then:
    result.totalCount == 6
    result.appliedTestsCount == 5
    result.totalTestsCount == 6
    result.breaksCount == 5
    result.resolvedCount == 3
    result.verifiedCount == 1
    result.finalizingCount == 1
    result.heldCount == 1
    result.progressKey == "dashboardId"
  }

  def "should return workflow preliminary breaks count"() {
    setup:
    def  pi = [
      new InstrumentResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true, value: ONE))
    ]
    def piNullValue = [
      new InstrumentResultBreak(threshold: ONE, providerValue: new EntryResultBreakByProvider(triggered: true))
    ]
    def piSkipped = [new InstrumentResultBreak(providerValue: ofBreakWithoutLevel(false, null))]
    def stateDate = BitemporalDate.newOfNow()

    def context = new MdPreliminaryContext(
    stateDate,
    stateDate.actualDate.minusDays(2),
    "mdgId",
    "mdgName",
    EXC_RESULT_MAPPER.fromDefinition(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT),
    BID,
    null,
    [ProviderData.of("PROVIDER1", 1.0, 0.9, BID)],
    pi, true
    )

    def contextNullValue = new MdPreliminaryContext(
    stateDate,
    stateDate.actualDate.minusDays(2),
    "mdgId",
    "mdgName",
    EXC_RESULT_MAPPER.fromDefinition(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT),
    BID,
    null,
    [ProviderData.of("PROVIDER1", 1.0, 0.9, BID)],
    piNullValue, true
    )

    def contextSkipped = new MdPreliminaryContext(
    stateDate,
    stateDate.actualDate.minusDays(2),
    "mdgId",
    "mdgName",
    EXC_RESULT_MAPPER.fromDefinition(InstrumentDefinitionBuilder.DUMMY_INSTRUMENT),
    BID,
    null,
    [ProviderData.of("PROVIDER1", 1.0, 0.9, BID)],
    piSkipped, false
    )

    def p1 = new ProcessExecution(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdPreliminaryState(
    entryStatus: WAITING_RESOLUTION
    )
    )
    def p2 = new ProcessExecution(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdPreliminaryState(
    entryStatus: WAITING_RESOLUTION
    )
    )
    def p3 = new ProcessExecution(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdPreliminaryState(
    entryStatus: WAITING_APPROVAL
    )
    )
    def p4 = new ProcessExecution(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: contextNullValue,
    currentState: new MdPreliminaryState(
    entryStatus: VERIFIED
    )
    )
    def p5 = new ProcessExecution(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: contextSkipped,
    currentState: new MdPreliminaryState(
    entryStatus: VERIFIED
    )
    )
    def p6 = new ProcessExecution(
    processId: MD_XM_PRELIMINARY_PROCESS_ID,
    rootBusinessKey: "dashboardId",
    context: context,
    currentState: new MdPreliminaryState(
    entryStatus: WAITING_RESOLUTION
    )
    )

    operations.insertAll([p1, p2, p3, p4, p5, p6])
    def s1 = new StepInstance(
    executionId: p1.id,
    status: WorkflowStatus.ACTIVE,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdPreliminaryState()
    )
    def s2 = new StepInstance(
    executionId: p2.id,
    status: WorkflowStatus.FINALIZING,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdPreliminaryState()
    )
    def s3 = new StepInstance(
    executionId: p3.id,
    status: WorkflowStatus.DONE,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdPreliminaryState()
    )
    def s4 = new StepInstance(
    executionId: p4.id,
    status: WorkflowStatus.DONE,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdPreliminaryState()
    )
    def s6 = new StepInstance(
    executionId: p6.id,
    status: WorkflowStatus.HELD,
    reportable: true,
    stepId: 'decideResolution',
    initialState: new MdPreliminaryState()
    )
    operations.insertAll([s1, s2, s3, s4, s6])


    def unBrokenPrelim = new InstrumentResultPreliminary(taskId: "id", appliedTestsCount: 1, hasBreaks: false, instrument: new Instrument(instrumentType: FRA), breakTests: pi)
    operations.insert(unBrokenPrelim)

    when:
    def result = repository.preliminaryBreakCount(breaksFilterForm(), ["id&stepId=decideResolution&taskExceptionManagementType=PRELIMINARY"])

    then:
    result.totalCount == 7
    result.appliedTestsCount == 6
    result.totalTestsCount == 7
    result.breaksCount == 5
    result.resolvedCount == 3
    result.verifiedCount == 1
    result.finalizingCount == 1
    result.heldCount == 1
    result.progressKey == "dashboardId"
  }

  def "should correctly return preliminary task filter by tasks ids"() {
    setup:
    def r = new ExceptionManagementResult()
    operations.insert(r)

    def instrument = new Instrument(assetClass: IR_RATE,
    instrumentType: IBOR_FIXING_DEPOSIT,
    currency: "EUR", assetName: "EUR 1M", sector: CreditSector.FINANCIALS)
    def instrument2 = new Instrument(assetClass: IR_RATE,
    instrumentType: IBOR_FIXING_DEPOSIT,
    currency: "USD", assetName: "USD 1M", sector: CreditSector.GOVERNMENT)

    def p = new InstrumentResultPreliminary(
    instrument: instrument,
    providerData: new ProviderData(provider: "BBG"),
    taskId: "taskId",
    hasBreaks: true,
    maxTriggeredThresholdLevel: 1,
    breakTests: [
      new InstrumentResultBreak(breakTestName: "Test1", providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(breakTestName: "Test2", providerValue: new EntryResultBreakByProvider(triggered: false))
    ]
    )
    def p2 = new InstrumentResultPreliminary(
    instrument: instrument2,
    providerData: new ProviderData(provider: "BBG"),
    taskId: "taskId2",
    hasBreaks: true,
    maxTriggeredThresholdLevel: 1,
    breakTests: [
      new InstrumentResultBreak(breakTestName: "Test1", providerValue: new EntryResultBreakByProvider(triggered: true))
    ]
    )
    operations.insertAll([p, p2])

    when:
    def result = repository.getPreliminaryTasksFilter(["taskId", "taskId2"], filterForm())

    then:
    result.rateCcys.size() == 2
    result.assetClasses.size() == 1
    result.assetClasses.contains(CountedFilter.newOf(IR_RATE, 2L))
    result.assetNames.size() == 2
    result.sectors.size() == 2
    result.providers.size() == 1
    result.providers.contains(CountedFilter.newOf("BBG", 2L))
    result.maxTriggeredThresholdLevels.size() == 1
    result.maxTriggeredThresholdLevels.contains(CountedFilter.newOf(1, 2L))
    result.breakingTests.size() == 2
    result.breakingTests.contains(CountedFilter.newOf("Test1", 2L))
    result.breakingTests.contains(CountedFilter.newOf("Test2", 0L))
  }

  def "should delete overlay data"() {
    setup:
    def xm = new ExceptionManagementResult(dashboardId: MD_DASHBOARD_ID)
    operations.insert(xm)

    def instrumentResultOverlay = new InstrumentResultOverlay(dashboardId: MD_DASHBOARD_ID)
    operations.insert(instrumentResultOverlay)

    when:
    repository.deleteOverlay(MD_DASHBOARD_ID)

    then:
    operations.findAll(ExceptionManagementResult).isEmpty()
    operations.findAll(InstrumentResultOverlay).isEmpty()
  }

  def "should correctly return overlay task filter by task ids"() {
    setup:
    def r = new ExceptionManagementResult()
    operations.insert(r)

    def instrument = new Instrument(assetClass: IR_RATE,
    instrumentType: IBOR_FIXING_DEPOSIT,
    currency: "EUR", assetName: "EUR 1M", sector: CreditSector.FINANCIALS)
    def instrument2 = new Instrument(assetClass: IR_RATE,
    instrumentType: IBOR_FIXING_DEPOSIT,
    currency: "USD", assetName: "USD 1M", sector: CreditSector.GOVERNMENT)

    def p = new InstrumentResultOverlay(instrument: instrument,
    taskId: "taskId",
    curveConfigurationId: "configurationId",
    hasBreaks: true,
    maxTriggeredThresholdLevel: 1,
    breakTests: [
      new InstrumentResultBreak(breakTestName: "Test1", providerValue: new EntryResultBreakByProvider(triggered: true)),
      new InstrumentResultBreak(breakTestName: "Test2", providerValue: new EntryResultBreakByProvider(triggered: false))
    ]
    )
    def p2 = new InstrumentResultOverlay(instrument: instrument2,
    taskId: "taskId2",
    curveConfigurationId: "configurationId",
    hasBreaks: true,
    breakTests: [
      new InstrumentResultBreak(breakTestName: "Test1", providerValue: new EntryResultBreakByProvider(triggered: true))
    ]
    )
    operations.insertAll([p, p2])

    when:
    def result = repository.getOverlayTasksFilter(["taskId"], filterForm())

    then:
    result.rateCcys.size() == 1
    result.rateCcys.contains(CountedFilter.newOf("EUR", 1L))
    result.assetClasses.contains(CountedFilter.newOf(IR_RATE, 1L))
    result.assetNames.contains(CountedFilter.newOf("EUR 1M", 1L))
    result.sectors.contains(CountedFilter.newOf(CreditSector.FINANCIALS, 1L))
    result.curveConfigurationIds.contains(CountedFilter.newOf("configurationId", 1L))
    result.maxTriggeredThresholdLevels.size() == 1
    result.maxTriggeredThresholdLevels.contains(CountedFilter.newOf(1, 1L))
    result.breakingTests.size() == 2
    result.breakingTests.contains(CountedFilter.newOf("Test1", 1L))
    result.breakingTests.contains(CountedFilter.newOf("Test2", 0L))
  }

  def "should correctly set overlay items task ids for Curve Config instruments"() {
    setup:
    def p1 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    curveConfigurationId: "cId",
    status: WAITING_RESOLUTION,
    instrument: new Instrument())
    def p1_1 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    curveConfigurationId: "cId1",
    status: WAITING_RESOLUTION,
    instrument: new Instrument())
    def p2 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    curveConfigurationId: "cId",
    instrument: new Instrument(assetClass: CoreAssetClass.CDS),
    status: VERIFIED)
    def p3 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    curveConfigurationId: "cId",
    status: WAITING_RESOLUTION,
    taskId: "task")
    def p4 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    curveConfigurationId: "cId",
    status: WAITING_RESOLUTION,
    taskId: "task")

    operations.insertAll([p1, p1_1, p2, p3, p4])

    when:
    def result = repository.setOverlayTaskIds(
    new TaskExecution(
    assetFilter: new AssetFilter(assetClasses: [CoreAssetClass.CDS]),
    id: "taskId1", exceptionManagementId: "id",
    curveConfigurationId: "cId")
    )

    then:
    result.isEmpty()
    operations.findAll(InstrumentResultOverlay)
    .stream()
    .filter({ t -> t.taskId == "taskId1" })
    .count() == 1
  }

  def "should correctly set overlay items task ids for TRS instruments"() {
    setup:
    def p1 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    companyId: "cId",
    status: WAITING_RESOLUTION,
    instrument: new Instrument())
    def p1_1 = new InstrumentResultOverlay(
    exceptionManagementResultId: "id",
    companyId: "cId",
    legalEntityId: "entityId",
    status: WAITING_RESOLUTION,
    instrument: new Instrument())

    operations.insertAll([p1, p1_1])

    when:
    def result = repository.setOverlayTaskIds(
    new TaskExecution(
    assetFilter: new AssetFilter(),
    id: "taskId1",
    exceptionManagementId: "id",
    companyId: "cId")
    )

    then:
    result.isEmpty()
    operations.findAll(InstrumentResultOverlay)
    .stream()
    .filter({ t -> t.taskId == "taskId1" })
    .filter({ t -> t.id == p1.id })
    .count() == 1

    when:
    repository.setOverlayTaskIds(
    new TaskExecution(
    assetFilter: new AssetFilter(),
    id: "taskId2",
    exceptionManagementId: "id",
    companyId: "cId",
    legalEntityId: "entityId")
    )
    then:
    result.isEmpty()
    operations.findAll(InstrumentResultOverlay)
    .stream()
    .filter({ t -> t.taskId == "taskId2" })
    .filter({ t -> t.id == p1_1.id })
    .count() == 1
  }

  def "should correctly set preliminary items task ids"() {
    setup:
    def p1 = new InstrumentResultPreliminary(
    exceptionManagementResultId: "id",
    status: WAITING_RESOLUTION,
    instrument: new Instrument())
    def p1_1 = new InstrumentResultPreliminary(
    exceptionManagementResultId: "id",
    status: WAITING_RESOLUTION,
    instrument: new Instrument())
    def p2 = new InstrumentResultPreliminary(
    exceptionManagementResultId: "id",
    instrument: new Instrument(assetClass: CoreAssetClass.CDS),
    status: VERIFIED)
    def p3 = new InstrumentResultPreliminary(
    exceptionManagementResultId: "id",
    status: WAITING_RESOLUTION,
    taskId: "task")
    def p4 = new InstrumentResultPreliminary(
    exceptionManagementResultId: "id",
    status: WAITING_RESOLUTION,
    taskId: "task")

    operations.insertAll([p1, p1_1, p2, p3, p4])

    when:
    def result = repository.setPreliminaryTaskIds(
    new TaskExecution(
    assetFilter: new AssetFilter(assetClasses: [CoreAssetClass.CDS]),
    id: "taskId1",
    exceptionManagementId: "id"
    )
    )

    then:
    result.isEmpty()
    operations.findAll(InstrumentResultPreliminary)
    .stream()
    .filter({ t -> t.taskId == "taskId1" })
    .count() == 1
  }

  def "should get dashboard dates overviews"() {
    setup:
    operations.insert(ExceptionManagementResult.batchPreliminary(
    MD_BATCH_DASHBOARD_ID,
    DASHBOARD_DATE_RANGE.startDate,
    MD_BATCH_DASHBOARD.mdExceptionManagementSetup,
    TRS_MARKET_DATA_DASHBOARD.trsMdExceptionManagementSetup
    ).tap { it ->
      it.status = BATCH_PROCESSING
      return it
    })

    operations.insert(ExceptionManagementResult.batchPreliminary(
    MD_BATCH_DASHBOARD_ID,
    DASHBOARD_DATE_RANGE.endDate,
    MD_BATCH_DASHBOARD.mdExceptionManagementSetup,
    TRS_MARKET_DATA_DASHBOARD.trsMdExceptionManagementSetup
    ).tap { it ->
      it.status = PRELIMINARY_BATCH_APPROVED
      return it
    })

    def result

    when:
    result = repository.getDashboardDatesOverviews(
    MD_BATCH_DASHBOARD_ID,
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 2

    result.content[0].date == DASHBOARD_DATE_RANGE.startDate
    result.content[0].status == BATCH_PROCESSING

    result.content[1].date == DASHBOARD_DATE_RANGE.endDate
    result.content[1].status == PRELIMINARY_BATCH_APPROVED

    when: "filter by status"
    result = repository.getDashboardDatesOverviews(
    MD_BATCH_DASHBOARD_ID,
    new TableFilter(List.of(new SimpleFilterClause(
    "status",
    FilterOperation.EQUAL,
    PRELIMINARY_BATCH_APPROVED.name())
    )),
    ScrollRequest.of(0, 10)
    )

    then:
    result.content.size() == 1

    result.content[0].date == DASHBOARD_DATE_RANGE.endDate
    result.content[0].status == PRELIMINARY_BATCH_APPROVED
  }

  static InstrumentFilterForm filterForm() {
    return new InstrumentFilterForm(
    providers: [],
    curveConfigurations: [],
    assetFilter: new AssetFilterForm(
    assetClasses: [],
    irInstruments: [],
    rateCcys: [],
    creditSectors: [],
    fxPairs: []
    ),
    curves: []
    )
  }

  static def breaksFilterForm() {
    new TaskInstrumentResultBreaksFilterForm(
    null,
    false,
    new InstrumentFilterForm([], [], new AssetFilterForm([], [], [], [], []), [], [], [], [])
    )
  }

  static def displayFilterForm(Boolean onlyCurvesWithBreaks = false, Boolean includeHeld = false, Boolean onboardingOnly = false) {
    return new ResultDisplayFilterForm(onlyCurvesWithBreaks, includeHeld, onboardingOnly)
  }
}
